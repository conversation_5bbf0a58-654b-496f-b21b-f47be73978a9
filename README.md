# Web Delivery Platform

A comprehensive web delivery management system built with Next.js, featuring order management, customer tracking, team management, fleet management, and reporting.

## Features

- Multi-language support (English and Arabic)
- Order management
- Online order processing
- Customer management
- Team management
- Fleet management
- Comprehensive reporting and analytics
- Responsive design for all devices

## Tech Stack

- Next.js 15.3
- TypeScript
- Firebase/Firestore
- next-intl for internationalization
- Tailwind CSS for styling

## Development

```bash
# Install dependencies
npm install --legacy-peer-deps

# Run the development server
npm run dev
```

## Deployment to Vercel

This project is configured for easy deployment to Vercel:

1. Push your code to a Git repository (GitHub, GitLab, or Bitbucket)
2. Import the project in the Vercel dashboard
3. Configure environment variables if needed
4. Deploy

Alternatively, you can deploy using the Vercel CLI:

```bash
# Deploy using Vercel CLI
npx vercel
```

## Environment Variables

Create a `.env.local` file with the following variables:

```
# Firebase configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_auth_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_storage_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

## 🚀 Features

### 🔐 Enhanced Authentication
- Beautiful login page with ZAWAYA branding
- Gradient design with modern UI/UX
- Demo credentials for easy testing
- Form validation and loading states
- **🆕 Biometric Authentication** - Fingerprint, Face ID, Windows Hello support
- WebAuthn integration for secure passwordless login
- Biometric settings management in dashboard

### 📊 Comprehensive Dashboard
- Real-time metrics and KPIs
- Interactive data visualization
- Order management system
- Active delivery tracking
- Fleet management
- Customer management
- Reports and analytics

### 🚛 Delivery Management
- Real-time delivery tracking
- Driver assignment and management
- Route optimization
- Status updates (On Time, Delayed, Early)
- ETA calculations
- Priority-based delivery scheduling

### 📦 Order Management
- Complete order lifecycle management
- Order status tracking (Processing, In Transit, Delivered, Cancelled)
- Customer information management
- Order history and analytics
- Bulk operations support
- **🆕 Email Notifications** - Automatic email alerts for delivered and cancelled orders

### 🎨 Modern UI/UX
- Responsive design for all devices
- Mobile-first approach
- Tailwind CSS for styling
- Lucide React icons
- Smooth animations and transitions
- Dark/light theme support

## 🛠️ Technology Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Backend**: Next.js API Routes
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth
- **Email**: Nodemailer for notifications
- **Deployment**: Vercel ready

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd zawaya-delivery
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Configure email notifications (optional):
```bash
cp .env.example .env.local
# Edit .env.local with your email credentials
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

### Demo Credentials
- **Email**: <EMAIL>
- **Password**: demo123

### Email Setup
For email notifications when orders are delivered or cancelled, see [EMAIL_SETUP.md](EMAIL_SETUP.md) for detailed configuration instructions.

## 📁 Project Structure

```
zawaya-delivery/
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   ├── orders/          # Orders API endpoints
│   │   │   ├── deliveries/      # Deliveries API endpoints
│   │   │   └── metrics/         # Metrics API endpoints
│   │   ├── dashboard/           # Dashboard page
│   │   ├── globals.css          # Global styles
│   │   ├── layout.tsx           # Root layout
│   │   └── page.tsx             # Login page
│   └── components/              # Reusable components (future)
├── public/                      # Static assets
├── tailwind.config.js          # Tailwind configuration
├── tsconfig.json               # TypeScript configuration
└── package.json                # Dependencies and scripts
```

## 🔌 API Endpoints

### Orders API (`/api/orders`)
- `GET` - Fetch all orders with filtering options
- `POST` - Create new order
- `PUT` - Update order status

### Deliveries API (`/api/deliveries`)
- `GET` - Fetch all deliveries with filtering options
- `POST` - Create new delivery
- `PUT` - Update delivery status and location

### Metrics API (`/api/metrics`)
- `GET` - Fetch dashboard metrics and analytics
- `POST` - Update metrics (for admin use)

## 🎯 Key Features Implemented

### Login Page
- ✅ ZAWAYA branding with gradient logo
- ✅ Modern form design with icons
- ✅ Password visibility toggle
- ✅ Remember me functionality
- ✅ Responsive design
- ✅ Loading states and animations
- ✅ Demo credentials display

### Dashboard
- ✅ Sidebar navigation with ZAWAYA branding
- ✅ Mobile-responsive sidebar
- ✅ Real-time metrics cards
- ✅ Recent orders table
- ✅ Active deliveries list
- ✅ Search functionality
- ✅ Welcome banner with actions
- ✅ Loading states and animations

### Backend APIs
- ✅ RESTful API design
- ✅ Mock data for development
- ✅ Error handling
- ✅ Filtering and pagination support
- ✅ TypeScript interfaces

## 🔮 Future Enhancements

- [ ] Real database integration (Supabase)
- [ ] User authentication with NextAuth.js
- [ ] Real-time updates with WebSockets
- [ ] Map integration for delivery tracking
- [ ] Push notifications
- [ ] Advanced analytics and reporting
- [ ] Multi-language support
- [ ] Role-based access control
- [ ] Mobile app (React Native)
- [ ] Integration with shipping providers

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy with one click

### Manual Deployment
```bash
npm run build
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please contact the ZAWAYA development team.

---

**Built with ❤️ for ZAWAYA by the development team**

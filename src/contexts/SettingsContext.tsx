'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { UserPreferencesService, UserSettings } from '@/services/firebase/userPreferences';

// Helper to check if we're in a browser environment
const isBrowser = (): boolean => {
  return typeof window !== 'undefined';
};

// UserSettings interface is now imported from the service

interface SettingsContextType {
  settings: UserSettings;
  updateSettings: (newSettings: Partial<UserSettings>) => Promise<void>;
  loading: boolean;
  error: string | null;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

// Default settings are now handled by the service

export const SettingsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [settings, setSettings] = useState<UserSettings>(UserPreferencesService.getDefaultSettings());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadSettings = useCallback(async () => {
    if (!user || !isBrowser()) return;

    try {
      setLoading(true);
      setError(null);

      // Load from Firestore
      const userPreferences = await UserPreferencesService.getUserPreferences(user.uid);

      if (userPreferences) {
        setSettings({
          ...userPreferences.settings,
          displayName: user?.displayName || userPreferences.settings.displayName || '',
          email: user?.email || userPreferences.settings.email || ''
        });
      } else {
        // Create default settings with user info
        const defaultSettings = UserPreferencesService.getDefaultSettings();
        const newSettings = {
          ...defaultSettings,
          displayName: user?.displayName || '',
          email: user?.email || ''
        };

        // Create user preferences in Firestore (without rememberedEmail to avoid undefined)
        await UserPreferencesService.createUserPreferences(user.uid, newSettings);
        setSettings(newSettings);
      }
    } catch (err) {
      console.error('Error loading settings:', err);
      setError('Failed to load settings');
      // Fallback to default settings
      const defaultSettings = UserPreferencesService.getDefaultSettings();
      setSettings({
        ...defaultSettings,
        displayName: user?.displayName || '',
        email: user?.email || ''
      });
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (user && isBrowser()) {
      loadSettings();
    }
  }, [user, loadSettings]);

  const updateSettings = async (newSettings: Partial<UserSettings>) => {
    if (!user || !isBrowser()) return;

    try {
      setLoading(true);
      setError(null);

      const updatedSettings = { ...settings, ...newSettings };

      // Save to Firestore
      await UserPreferencesService.updateUserSettings(user.uid, newSettings);

      // Update state
      setSettings(updatedSettings);

      // Apply theme changes immediately
      if (newSettings.theme && isBrowser()) {
        const root = document.documentElement;
        if (newSettings.theme === 'system') {
          const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
          root.classList.toggle('dark', systemTheme === 'dark');
        } else {
          root.classList.toggle('dark', newSettings.theme === 'dark');
        }
      }

      console.log('Settings updated successfully:', updatedSettings);
    } catch (err) {
      console.error('Error updating settings:', err);
      setError('Failed to update settings');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return (
    <SettingsContext.Provider value={{ settings, updateSettings, loading, error }}>
      {children}
    </SettingsContext.Provider>
  );
};

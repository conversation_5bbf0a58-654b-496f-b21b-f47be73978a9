'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ApprovedOrdersService, ApprovedOrder as FirebaseApprovedOrder } from '@/services/firebase/approvedOrders';
import { useAuth } from './AuthContext';

// Types for approved orders in purchasing
export interface ApprovedOrderItem {
  name: string;
  quantity: number;
  price: string;
}

// Re-export the Firebase ApprovedOrder interface for compatibility
export type ApprovedOrder = FirebaseApprovedOrder;

interface OrdersContextType {
  approvedOrders: ApprovedOrder[];
  loading: boolean;
  addApprovedOrder: (order: Omit<ApprovedOrder, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateOrderStatus: (orderId: string, status: 'Pending' | 'Approved' | 'Cancelled', updatedBy?: string) => Promise<void>;
  removeOrder: (orderId: string) => Promise<void>;
  getOrderById: (orderId: string) => ApprovedOrder | undefined;
  moveToMainOrders: (orderId: string) => Promise<boolean>;
  cancelOrder: (orderId: string, reason: string, notes?: string, cancelledBy?: string) => Promise<void>;
}

const OrdersContext = createContext<OrdersContextType | undefined>(undefined);

export const useOrders = () => {
  const context = useContext(OrdersContext);
  if (context === undefined) {
    throw new Error('useOrders must be used within an OrdersProvider');
  }
  return context;
};

interface OrdersProviderProps {
  children: ReactNode;
}

export const OrdersProvider: React.FC<OrdersProviderProps> = ({ children }) => {
  const [approvedOrders, setApprovedOrders] = useState<ApprovedOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  // Load approved orders from Firebase on mount and set up real-time listener
  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }

    // Set up real-time listener for approved orders
    const unsubscribe = ApprovedOrdersService.subscribeToApprovedOrders((orders) => {
      setApprovedOrders(orders);
      setLoading(false);
      console.log('Loaded approved orders from Firebase:', orders.length);
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [user]);

  const addApprovedOrder = async (orderData: Omit<ApprovedOrder, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      await ApprovedOrdersService.createApprovedOrder(orderData);
      // The real-time listener will automatically update the state
      console.log('Approved order added successfully');
    } catch (error) {
      console.error('Error adding approved order:', error);
      throw error;
    }
  };

  const updateOrderStatus = async (orderId: string, status: 'Pending' | 'Approved' | 'Cancelled', updatedBy?: string) => {
    try {
      await ApprovedOrdersService.updateApprovedOrderStatus(orderId, status, updatedBy || user?.displayName || user?.email);
      // The real-time listener will automatically update the state
      console.log(`Order status updated: ${orderId} -> ${status}`);
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  };

  const removeOrder = async (orderId: string) => {
    try {
      await ApprovedOrdersService.deleteApprovedOrder(orderId);
      // The real-time listener will automatically update the state
      console.log(`Order removed: ${orderId}`);
    } catch (error) {
      console.error('Error removing order:', error);
      throw error;
    }
  };

  const getOrderById = (orderId: string) => {
    return approvedOrders.find(order => order.id === orderId);
  };

  const moveToMainOrders = async (orderId: string): Promise<boolean> => {
    try {
      const success = await ApprovedOrdersService.moveToMainOrders(orderId, user?.uid);
      if (success) {
        console.log(`Order ${orderId} moved to main orders successfully`);
      }
      return success;
    } catch (error) {
      console.error('Error moving order to main orders:', error);
      return false;
    }
  };

  const cancelOrder = async (orderId: string, reason: string, notes?: string, cancelledBy?: string) => {
    try {
      await ApprovedOrdersService.cancelApprovedOrder(
        orderId,
        reason,
        notes,
        cancelledBy || user?.displayName || user?.email || 'Unknown'
      );
      // The real-time listener will automatically update the state
      console.log(`Order cancelled: ${orderId}`);
    } catch (error) {
      console.error('Error cancelling order:', error);
      throw error;
    }
  };

  const value: OrdersContextType = {
    approvedOrders,
    loading,
    addApprovedOrder,
    updateOrderStatus,
    removeOrder,
    getOrderById,
    moveToMainOrders,
    cancelOrder
  };

  return (
    <OrdersContext.Provider value={value}>
      {children}
    </OrdersContext.Provider>
  );
};

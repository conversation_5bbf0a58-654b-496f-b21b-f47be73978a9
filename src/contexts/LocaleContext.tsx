'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useSettings } from './SettingsContext';
import { type Locale } from '@/i18n/config';

// Helper to check if we're in a browser environment
const isBrowser = (): boolean => {
  return typeof window !== 'undefined';
};

interface LocaleContextType {
  locale: Locale;
  direction: 'ltr' | 'rtl';
  changeLocale: (newLocale: Locale) => void;
}

const LocaleContext = createContext<LocaleContextType | undefined>(undefined);

export const useLocale = () => {
  const context = useContext(LocaleContext);
  if (!context) {
    throw new Error('useLocale must be used within a LocaleProvider');
  }
  return context;
};

export const LocaleProvider: React.FC<{
  children: React.ReactNode;
  initialLocale: Locale;
}> = ({ children, initialLocale }) => {
  const [locale, setLocale] = useState<Locale>(initialLocale);
  const [direction, setDirection] = useState<'ltr' | 'rtl'>('ltr'); // Force LTR for all languages
  const { settings, updateSettings } = useSettings();
  const router = useRouter();
  const pathname = usePathname() || '';

  // Initialize locale from URL path on mount
  useEffect(() => {
    if (!isBrowser()) return;
    
    const pathParts = pathname.split('/');
    const pathLocale = pathParts.length > 1 ? pathParts[1] as Locale : null;
    if (pathLocale && ['en', 'ar'].includes(pathLocale) && pathLocale !== locale) {
      setLocale(pathLocale);
      // Update settings to persist the locale preference
      updateSettings({ language: pathLocale }).catch(console.error);
    }
  }, [pathname, locale, updateSettings]);

  // Sync with settings context only if settings language differs from current URL locale
  useEffect(() => {
    if (!isBrowser()) return;
    
    const pathParts = pathname.split('/');
    const pathLocale = pathParts.length > 1 ? pathParts[1] as Locale : null;
    if (settings.language && pathLocale && settings.language !== pathLocale && ['en', 'ar'].includes(settings.language)) {
      // Only navigate if we're not already on the correct locale path
      if (pathLocale !== settings.language) {
        const newLocale = settings.language as Locale;
        setLocale(newLocale);
        setDirection('ltr'); // Force LTR

        // Update document direction - Force LTR
        document.documentElement.dir = 'ltr';
        document.documentElement.lang = newLocale;
      }
    }
  }, [settings.language, pathname]);

  // Update direction when locale changes
  useEffect(() => {
    if (!isBrowser()) return;
    
    setDirection('ltr'); // Force LTR
    document.documentElement.dir = 'ltr'; // Force LTR
    document.documentElement.lang = locale;
  }, [locale]);

  const changeLocale = async (newLocale: Locale) => {
    if (!isBrowser()) return;
    
    try {
      // Update settings context
      await updateSettings({ language: newLocale });

      // Update local state
      setLocale(newLocale);
      setDirection('ltr'); // Force LTR

      // Navigate to the new locale path
      // Remove current locale from path (handles both /en and /ar)
      let currentPath = pathname.replace(/^\/[a-z]{2}/, '');

      // Ensure we have a valid path
      if (!currentPath || currentPath === '') {
        currentPath = '/dashboard'; // Default to dashboard
      }

      // Ensure path starts with /
      if (!currentPath.startsWith('/')) {
        currentPath = '/' + currentPath;
      }

      // Build new path based on target locale
      // With localePrefix: 'always', both languages need prefixes
      const newPath = `/${newLocale}${currentPath}`;

      console.log('Changing locale:', {
        originalPathname: pathname,
        currentPath,
        newPath,
        newLocale
      });

      router.push(newPath);
    } catch (error) {
      console.error('Error changing locale:', error);
    }
  };

  return (
    <LocaleContext.Provider value={{ locale, direction, changeLocale }}>
      {children}
    </LocaleContext.Provider>
  );
};

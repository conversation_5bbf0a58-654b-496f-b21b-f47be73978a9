import { getRequestConfig } from 'next-intl/server';
import { locales, defaultLocale, type Locale } from './config';

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming locale is supported
  let validLocale = locale as Locale;
  if (!locale || !locales.includes(validLocale)) {
    validLocale = defaultLocale;
  }

  return {
    locale: validLocale,
    messages: (await import(`../../messages/${validLocale}.json`)).default
  };
}); 
// Import the functions you need from the SDKs you need
import { initializeApp, getApps, getApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import type { Auth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import type { Firestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import type { FirebaseStorage } from "firebase/storage";
import type { Analytics } from "firebase/analytics";
import type { Messaging } from "firebase/messaging";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyDGZ5lIgPxC1c2FksxIa8Ak7ngehw-G3n8",
  authDomain: "web-delivery-61cae.firebaseapp.com",
  projectId: "web-delivery-61cae",
  storageBucket: "web-delivery-61cae.firebasestorage.app",
  messagingSenderId: "158352075738",
  appId: "1:158352075738:web:c83d5ef788fcd6865b6ee3",
  measurementId: "G-9QFNF53DJY"
};

// Initialize Firebase with SSR safety
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();

// Initialize Firebase services with SSR safety
let auth: Auth;
let db: Firestore;
let storage: FirebaseStorage;
let analytics: Analytics | null = null;
let messaging: Messaging | null = null;

// Only initialize client-side services in browser environment
if (typeof window !== 'undefined') {
  // Auth
  auth = getAuth(app);
  
  // Firestore
  db = getFirestore(app);
  
  // Storage
  storage = getStorage(app);
  
  // Analytics (only in browser)
  const initAnalytics = async () => {
    try {
      const { getAnalytics } = await import("firebase/analytics");
      analytics = getAnalytics(app);
    } catch {
      // Analytics not available
    }
  };
  
  // Messaging (only in browser)
  const initMessaging = async () => {
    try {
      const { getMessaging } = await import("firebase/messaging");
      messaging = getMessaging(app);
    } catch {
      // Messaging not available
    }
  };
  
  // Initialize optional services
  initAnalytics();
  initMessaging();
} else {
  // Server-side initialization with minimal setup
  auth = getAuth(app);
  db = getFirestore(app);
  storage = getStorage(app);
}

export { auth, db, storage, analytics, messaging };
export default app;

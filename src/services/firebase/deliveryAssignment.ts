import { TeamService } from './team';
import { OrdersService } from './orders';

export interface DeliveryAgent {
  id: string;
  name: string;
  phone: string;
  status: 'Active' | 'Inactive' | 'On Leave';
  currentOrders?: number;
}

export class DeliveryAssignmentService {
  // Get available delivery agents
  static async getAvailableDeliveryAgents(): Promise<DeliveryAgent[]> {
    try {
      const teamMembers = await TeamService.getAllTeamMembers();
      
      // Filter for active delivery agents
      const deliveryAgents = teamMembers
        .filter(member => 
          member.role === 'Delivery' && 
          member.status === 'Active'
        )
        .map(member => ({
          id: member.id,
          name: member.name,
          phone: member.phone,
          status: member.status,
          currentOrders: 0 // Will be calculated below
        }));

      // Get current order counts for each agent
      const allOrders = await OrdersService.getAllOrders();
      const activeOrders = allOrders.filter(order => 
        ['Started', 'Moved to Supplier', 'Arrived at Supplier', 'Moving to Customer', 'Arrived at Customer'].includes(order.status)
      );

      // Count orders per delivery agent
      const orderCounts = new Map<string, number>();
      activeOrders.forEach(order => {
        if (order.deliveryAgent) {
          const count = orderCounts.get(order.deliveryAgent) || 0;
          orderCounts.set(order.deliveryAgent, count + 1);
        }
      });

      // Update current order counts
      deliveryAgents.forEach(agent => {
        agent.currentOrders = orderCounts.get(agent.name) || 0;
      });

      return deliveryAgents;
    } catch (error) {
      console.error('Error getting available delivery agents:', error);
      return [];
    }
  }

  // Auto-assign delivery agent based on workload
  static async autoAssignDeliveryAgent(): Promise<string | null> {
    try {
      const availableAgents = await this.getAvailableDeliveryAgents();
      
      if (availableAgents.length === 0) {
        console.warn('No available delivery agents found');
        return null;
      }

      // Sort by current workload (ascending) to assign to least busy agent
      availableAgents.sort((a, b) => (a.currentOrders || 0) - (b.currentOrders || 0));
      
      const selectedAgent = availableAgents[0];
      console.log(`Auto-assigned delivery agent: ${selectedAgent.name} (current orders: ${selectedAgent.currentOrders})`);
      
      return selectedAgent.name;
    } catch (error) {
      console.error('Error auto-assigning delivery agent:', error);
      return null;
    }
  }

  // Assign delivery agent to order
  static async assignDeliveryAgentToOrder(orderId: string, agentName?: string): Promise<boolean> {
    try {
      // If no agent specified, auto-assign
      const deliveryAgent = agentName || await this.autoAssignDeliveryAgent();
      
      if (!deliveryAgent) {
        console.error('No delivery agent available for assignment');
        return false;
      }

      // Find the order by orderId
      const orderResult = await OrdersService.getOrderByOrderId(orderId);
      if (!orderResult) {
        console.error('Order not found:', orderId);
        return false;
      }

      // Update the order with delivery agent
      await OrdersService.updateOrder(orderResult.docId, {
        deliveryAgent: deliveryAgent
      });

      console.log(`Assigned delivery agent ${deliveryAgent} to order ${orderId}`);
      return true;
    } catch (error) {
      console.error('Error assigning delivery agent to order:', error);
      return false;
    }
  }

  // Reassign delivery agent
  static async reassignDeliveryAgent(orderId: string, newAgentName: string): Promise<boolean> {
    try {
      const orderResult = await OrdersService.getOrderByOrderId(orderId);
      if (!orderResult) {
        console.error('Order not found:', orderId);
        return false;
      }

      const oldAgent = orderResult.order.deliveryAgent;
      
      await OrdersService.updateOrder(orderResult.docId, {
        deliveryAgent: newAgentName
      });

      // Add action log for reassignment
      await OrdersService.addOrderAction(
        orderResult.docId,
        `Delivery agent reassigned from ${oldAgent || 'None'} to ${newAgentName}`,
        'System'
      );

      console.log(`Reassigned delivery agent from ${oldAgent} to ${newAgentName} for order ${orderId}`);
      return true;
    } catch (error) {
      console.error('Error reassigning delivery agent:', error);
      return false;
    }
  }

  // Get delivery agent workload summary
  static async getDeliveryAgentWorkload(): Promise<Array<{
    agent: string;
    activeOrders: number;
    orderIds: string[];
  }>> {
    try {
      const allOrders = await OrdersService.getAllOrders();
      const activeOrders = allOrders.filter(order => 
        ['Started', 'Moved to Supplier', 'Arrived at Supplier', 'Moving to Customer', 'Arrived at Customer'].includes(order.status)
      );

      const workloadMap = new Map<string, { activeOrders: number; orderIds: string[] }>();

      activeOrders.forEach(order => {
        if (order.deliveryAgent) {
          const current = workloadMap.get(order.deliveryAgent) || { activeOrders: 0, orderIds: [] };
          current.activeOrders++;
          current.orderIds.push(order.orderId);
          workloadMap.set(order.deliveryAgent, current);
        }
      });

      return Array.from(workloadMap.entries()).map(([agent, data]) => ({
        agent,
        activeOrders: data.activeOrders,
        orderIds: data.orderIds
      }));
    } catch (error) {
      console.error('Error getting delivery agent workload:', error);
      return [];
    }
  }
}

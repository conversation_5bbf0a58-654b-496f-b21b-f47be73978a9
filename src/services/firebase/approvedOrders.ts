import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  onSnapshot,
  Unsubscribe
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { prepareForFirebase } from '@/utils/firebase';
import { DeliveryAssignmentService } from './deliveryAssignment';
import { parseMonetaryAmount, debugOrderData, sanitizeOrderData } from '@/utils/orderUtils';

export interface ApprovedOrderItem {
  name: string;
  quantity: number;
  price: string;
}

export interface ApprovedOrder {
  id: string;
  customer: string;
  email: string;
  phone: string;
  items: ApprovedOrderItem[];
  total: string;
  status: 'Pending' | 'Approved' | 'Cancelled';
  orderDate: string;
  platform: string;
  paymentMethod: string;
  deliveryAddress: string;
  financialStatus: string;
  fulfillmentStatus: string;
  currency: string;
  tags?: string[];
  notes?: string;
  source?: string;
  // Additional fields for purchasing context
  approvedDate: string;
  originalOrderId: string;
  approvedBy?: string;
  // Firebase specific fields
  createdAt: Timestamp;
  updatedAt: Timestamp;
  // Cancellation details
  cancellationReason?: string;
  cancellationNotes?: string;
  cancelledBy?: string;
  cancelledAt?: Timestamp;
}

const APPROVED_ORDERS_COLLECTION = 'approvedOrders';

export class ApprovedOrdersService {
  // Get all approved orders
  static async getAllApprovedOrders(): Promise<ApprovedOrder[]> {
    try {
      const ordersRef = collection(db, APPROVED_ORDERS_COLLECTION);
      const q = query(ordersRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as ApprovedOrder));
    } catch (error) {
      console.error('Error fetching approved orders:', error);
      return [];
    }
  }

  // Get approved orders by status
  static async getApprovedOrdersByStatus(status: string): Promise<ApprovedOrder[]> {
    try {
      const ordersRef = collection(db, APPROVED_ORDERS_COLLECTION);
      const q = query(
        ordersRef,
        where('status', '==', status),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as ApprovedOrder));
    } catch (error) {
      console.error('Error fetching approved orders by status:', error);
      return [];
    }
  }

  // Get recent approved orders
  static async getRecentApprovedOrders(limitCount: number = 50): Promise<ApprovedOrder[]> {
    try {
      const ordersRef = collection(db, APPROVED_ORDERS_COLLECTION);
      const q = query(
        ordersRef,
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as ApprovedOrder));
    } catch (error) {
      console.error('Error fetching recent approved orders:', error);
      return [];
    }
  }

  // Get single approved order
  static async getApprovedOrder(id: string): Promise<ApprovedOrder | null> {
    try {
      const orderRef = doc(db, APPROVED_ORDERS_COLLECTION, id);
      const snapshot = await getDoc(orderRef);

      if (snapshot.exists()) {
        return {
          id: snapshot.id,
          ...snapshot.data()
        } as ApprovedOrder;
      }

      return null;
    } catch (error) {
      console.error('Error fetching approved order:', error);
      throw error;
    }
  }

  // Get approved order by original order ID
  static async getApprovedOrderByOriginalId(originalOrderId: string): Promise<ApprovedOrder | null> {
    try {
      const ordersRef = collection(db, APPROVED_ORDERS_COLLECTION);
      const q = query(ordersRef, where('originalOrderId', '==', originalOrderId));
      const snapshot = await getDocs(q);

      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        return {
          id: doc.id,
          ...doc.data()
        } as ApprovedOrder;
      }

      return null;
    } catch (error) {
      console.error('Error fetching approved order by original ID:', error);
      throw error;
    }
  }

  // Create new approved order
  static async createApprovedOrder(orderData: Omit<ApprovedOrder, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const now = Timestamp.now();
      const ordersRef = collection(db, APPROVED_ORDERS_COLLECTION);

      const cleanData = prepareForFirebase({
        ...orderData,
        createdAt: now,
        updatedAt: now
      });

      const docRef = await addDoc(ordersRef, cleanData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating approved order:', error);
      throw error;
    }
  }

  // Update approved order
  static async updateApprovedOrder(id: string, updates: Partial<ApprovedOrder>): Promise<void> {
    try {
      const orderRef = doc(db, APPROVED_ORDERS_COLLECTION, id);
      const cleanUpdates = prepareForFirebase({
        ...updates,
        updatedAt: Timestamp.now()
      });

      await updateDoc(orderRef, cleanUpdates);
    } catch (error) {
      console.error('Error updating approved order:', error);
      throw error;
    }
  }

  // Update approved order status
  static async updateApprovedOrderStatus(id: string, status: 'Pending' | 'Approved' | 'Cancelled', updatedBy?: string): Promise<void> {
    try {
      const updates: Partial<ApprovedOrder> = {
        status,
        updatedAt: Timestamp.now()
      };

      if (updatedBy) {
        if (status === 'Approved') {
          updates.approvedBy = updatedBy;
        } else if (status === 'Cancelled') {
          updates.cancelledBy = updatedBy;
          updates.cancelledAt = Timestamp.now();
        }
      }

      await this.updateApprovedOrder(id, updates);
    } catch (error) {
      console.error('Error updating approved order status:', error);
      throw error;
    }
  }

  // Cancel approved order with reason
  static async cancelApprovedOrder(
    id: string, 
    reason: string, 
    notes?: string, 
    cancelledBy?: string
  ): Promise<void> {
    try {
      const updates: Partial<ApprovedOrder> = {
        status: 'Cancelled',
        cancellationReason: reason,
        cancellationNotes: notes,
        cancelledBy,
        cancelledAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      await this.updateApprovedOrder(id, updates);
    } catch (error) {
      console.error('Error cancelling approved order:', error);
      throw error;
    }
  }

  // Delete approved order
  static async deleteApprovedOrder(id: string): Promise<void> {
    try {
      const orderRef = doc(db, APPROVED_ORDERS_COLLECTION, id);
      await deleteDoc(orderRef);
    } catch (error) {
      console.error('Error deleting approved order:', error);
      throw error;
    }
  }

  // Move approved order to main orders system
  static async moveToMainOrders(id: string, userId?: string): Promise<boolean> {
    try {
      const approvedOrder = await this.getApprovedOrder(id);
      if (!approvedOrder) {
        console.error('Approved order not found:', id);
        return false;
      }

      // Debug the approved order data
      debugOrderData(approvedOrder, 'Approved Order to Main Order Conversion');

      // Convert ApprovedOrder to main Order format
      // Parse total amount using utility function
      const totalAmount = parseMonetaryAmount(approvedOrder.total);

      const mainOrderData = {
        orderId: approvedOrder.originalOrderId,
        customerName: approvedOrder.customer,
        customerPhone: approvedOrder.phone,
        customerAddress: approvedOrder.deliveryAddress,
        orderType: 'Online Order',
        totalAmount: totalAmount,
        depositAmount: 0,
        status: 'Pending' as const,
        salesRep: 'Online Platform',
        // deliveryAgent will be auto-assigned after order creation
        items: approvedOrder.items.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: parseMonetaryAmount(item.price)
        })),
        createdBy: userId || 'System',
        creationLocation: { lat: 30.0444, lng: 31.2357 } // Default Cairo coordinates
      };

      // Sanitize the order data before sending
      const sanitizedOrderData = sanitizeOrderData(mainOrderData);
      debugOrderData(sanitizedOrderData, 'Sanitized Main Order Data');

      // Call API to create order in main system
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(sanitizedOrderData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to create order in main system: ${errorData.error || 'Unknown error'}`);
      }

      const result = await response.json();
      const newOrderId = sanitizedOrderData.orderId;

      // Auto-assign delivery agent to the new order
      try {
        const assignmentSuccess = await DeliveryAssignmentService.assignDeliveryAgentToOrder(newOrderId);
        if (assignmentSuccess) {
          console.log(`Auto-assigned delivery agent to order ${newOrderId}`);
        } else {
          console.warn(`Failed to auto-assign delivery agent to order ${newOrderId}`);
        }
      } catch (assignmentError) {
        console.error('Error during delivery agent assignment:', assignmentError);
        // Don't fail the entire operation if assignment fails
      }

      // Remove from approved orders
      await this.deleteApprovedOrder(id);

      console.log(`Approved order ${id} moved to main orders successfully with ID ${newOrderId}`);
      return true;
    } catch (error) {
      console.error('Error moving approved order to main orders:', error);
      return false;
    }
  }

  // Real-time listener for approved orders
  static subscribeToApprovedOrders(callback: (orders: ApprovedOrder[]) => void): Unsubscribe {
    const ordersRef = collection(db, APPROVED_ORDERS_COLLECTION);
    const q = query(ordersRef, orderBy('createdAt', 'desc'));

    return onSnapshot(q, (snapshot) => {
      const orders = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as ApprovedOrder));
      callback(orders);
    });
  }

  // Real-time listener for specific status
  static subscribeToApprovedOrdersByStatus(status: string, callback: (orders: ApprovedOrder[]) => void): Unsubscribe {
    const ordersRef = collection(db, APPROVED_ORDERS_COLLECTION);
    const q = query(
      ordersRef,
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(q, (snapshot) => {
      const orders = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as ApprovedOrder));
      callback(orders);
    });
  }
}

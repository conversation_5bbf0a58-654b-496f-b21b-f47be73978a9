import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  onSnapshot,
  Unsubscribe
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { prepareForFirebase } from '@/utils/firebase';

export interface OnlineOrderItem {
  name: string;
  quantity: number;
  price: string;
}

export interface OnlineOrder {
  id: string;
  customer: string;
  email: string;
  phone: string;
  items: OnlineOrderItem[];
  total: string;
  status: 'Pending' | 'Approved' | 'Processing' | 'Confirmed' | 'Shipped' | 'Delivered' | 'Cancelled';
  orderDate: string;
  platform: 'Shopify' | 'Website' | 'Mobile App' | 'Social Media';
  paymentMethod: string;
  deliveryAddress: string;
  trackingNumber?: string;
  financialStatus: string;
  fulfillmentStatus: string;
  currency: string;
  tags: string[];
  notes?: string;
  source: string;
  // Firebase specific fields
  createdAt: Timestamp;
  updatedAt: Timestamp;
  // Original Shopify/platform ID for reference
  originalOrderId: string;
  // Sync status
  syncedFromPlatform: boolean;
  lastSyncAt?: Timestamp;
}

const ONLINE_ORDERS_COLLECTION = 'onlineOrders';

export class OnlineOrdersService {
  // Get all online orders
  static async getAllOnlineOrders(): Promise<OnlineOrder[]> {
    try {
      const ordersRef = collection(db, ONLINE_ORDERS_COLLECTION);
      const q = query(ordersRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as OnlineOrder));
    } catch (error) {
      console.error('Error fetching online orders:', error);
      return [];
    }
  }

  // Get online orders by status
  static async getOnlineOrdersByStatus(status: string): Promise<OnlineOrder[]> {
    try {
      const ordersRef = collection(db, ONLINE_ORDERS_COLLECTION);
      const q = query(
        ordersRef,
        where('status', '==', status),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as OnlineOrder));
    } catch (error) {
      console.error('Error fetching online orders by status:', error);
      return [];
    }
  }

  // Get online orders by platform
  static async getOnlineOrdersByPlatform(platform: string): Promise<OnlineOrder[]> {
    try {
      const ordersRef = collection(db, ONLINE_ORDERS_COLLECTION);
      const q = query(
        ordersRef,
        where('platform', '==', platform),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as OnlineOrder));
    } catch (error) {
      console.error('Error fetching online orders by platform:', error);
      return [];
    }
  }

  // Get recent online orders
  static async getRecentOnlineOrders(limitCount: number = 50): Promise<OnlineOrder[]> {
    try {
      const ordersRef = collection(db, ONLINE_ORDERS_COLLECTION);
      const q = query(
        ordersRef,
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as OnlineOrder));
    } catch (error) {
      console.error('Error fetching recent online orders:', error);
      return [];
    }
  }

  // Get single online order
  static async getOnlineOrder(id: string): Promise<OnlineOrder | null> {
    try {
      const orderRef = doc(db, ONLINE_ORDERS_COLLECTION, id);
      const snapshot = await getDoc(orderRef);

      if (snapshot.exists()) {
        return {
          id: snapshot.id,
          ...snapshot.data()
        } as OnlineOrder;
      }

      return null;
    } catch (error) {
      console.error('Error fetching online order:', error);
      throw error;
    }
  }

  // Get online order by original platform ID
  static async getOnlineOrderByOriginalId(originalOrderId: string): Promise<OnlineOrder | null> {
    try {
      const ordersRef = collection(db, ONLINE_ORDERS_COLLECTION);
      const q = query(ordersRef, where('originalOrderId', '==', originalOrderId));
      const snapshot = await getDocs(q);

      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        return {
          id: doc.id,
          ...doc.data()
        } as OnlineOrder;
      }

      return null;
    } catch (error) {
      console.error('Error fetching online order by original ID:', error);
      throw error;
    }
  }

  // Create or update online order (for syncing from platforms)
  static async createOrUpdateOnlineOrder(orderData: Omit<OnlineOrder, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      // Check if order already exists by original ID
      const existingOrder = await this.getOnlineOrderByOriginalId(orderData.originalOrderId);
      
      if (existingOrder) {
        // Update existing order
        await this.updateOnlineOrder(existingOrder.id, {
          ...orderData,
          lastSyncAt: Timestamp.now()
        });
        return existingOrder.id;
      } else {
        // Create new order
        const now = Timestamp.now();
        const ordersRef = collection(db, ONLINE_ORDERS_COLLECTION);

        const cleanData = prepareForFirebase({
          ...orderData,
          createdAt: now,
          updatedAt: now,
          lastSyncAt: now
        });

        const docRef = await addDoc(ordersRef, cleanData);
        return docRef.id;
      }
    } catch (error) {
      console.error('Error creating/updating online order:', error);
      throw error;
    }
  }

  // Update online order
  static async updateOnlineOrder(id: string, updates: Partial<OnlineOrder>): Promise<void> {
    try {
      const orderRef = doc(db, ONLINE_ORDERS_COLLECTION, id);
      const cleanUpdates = prepareForFirebase({
        ...updates,
        updatedAt: Timestamp.now()
      });

      await updateDoc(orderRef, cleanUpdates);
    } catch (error) {
      console.error('Error updating online order:', error);
      throw error;
    }
  }

  // Delete online order
  static async deleteOnlineOrder(id: string): Promise<void> {
    try {
      const orderRef = doc(db, ONLINE_ORDERS_COLLECTION, id);
      await deleteDoc(orderRef);
    } catch (error) {
      console.error('Error deleting online order:', error);
      throw error;
    }
  }

  // Sync orders from external platform (like Shopify)
  static async syncOrdersFromPlatform(platformOrders: any[], platform: string): Promise<{ synced: number; errors: number }> {
    let synced = 0;
    let errors = 0;

    for (const platformOrder of platformOrders) {
      try {
        // Transform platform order to our format
        const onlineOrder: Omit<OnlineOrder, 'id' | 'createdAt' | 'updatedAt'> = {
          customer: platformOrder.customer || 'Unknown Customer',
          email: platformOrder.email || '',
          phone: platformOrder.phone || '',
          items: platformOrder.items || [],
          total: platformOrder.total || '0',
          status: platformOrder.status || 'Pending',
          orderDate: platformOrder.orderDate || new Date().toISOString(),
          platform: platform as any,
          paymentMethod: platformOrder.paymentMethod || 'Unknown',
          deliveryAddress: platformOrder.deliveryAddress || '',
          trackingNumber: platformOrder.trackingNumber,
          financialStatus: platformOrder.financialStatus || 'pending',
          fulfillmentStatus: platformOrder.fulfillmentStatus || 'unfulfilled',
          currency: platformOrder.currency || 'EGP',
          tags: platformOrder.tags || [],
          notes: platformOrder.notes,
          source: platformOrder.source || platform,
          originalOrderId: platformOrder.id,
          syncedFromPlatform: true
        };

        await this.createOrUpdateOnlineOrder(onlineOrder);
        synced++;
      } catch (error) {
        console.error(`Error syncing order ${platformOrder.id}:`, error);
        errors++;
      }
    }

    return { synced, errors };
  }

  // Real-time listener for online orders
  static subscribeToOnlineOrders(callback: (orders: OnlineOrder[]) => void): Unsubscribe {
    const ordersRef = collection(db, ONLINE_ORDERS_COLLECTION);
    const q = query(ordersRef, orderBy('createdAt', 'desc'));

    return onSnapshot(q, (snapshot) => {
      const orders = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as OnlineOrder));
      callback(orders);
    });
  }

  // Real-time listener for specific status
  static subscribeToOnlineOrdersByStatus(status: string, callback: (orders: OnlineOrder[]) => void): Unsubscribe {
    const ordersRef = collection(db, ONLINE_ORDERS_COLLECTION);
    const q = query(
      ordersRef,
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(q, (snapshot) => {
      const orders = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as OnlineOrder));
      callback(orders);
    });
  }
}

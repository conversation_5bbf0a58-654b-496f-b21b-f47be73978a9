import { NextRequest, NextResponse } from 'next/server';
import { OnlineOrdersService, OnlineOrder } from '@/services/firebase/onlineOrders';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const platform = searchParams.get('platform');
    const limit = searchParams.get('limit');

    let orders: OnlineOrder[];

    // Filter by specific status if provided
    if (status && status !== 'all') {
      orders = await OnlineOrdersService.getOnlineOrdersByStatus(status);
    } else if (platform) {
      // Filter by platform
      orders = await OnlineOrdersService.getOnlineOrdersByPlatform(platform);
    } else if (limit) {
      // Get recent orders with limit
      orders = await OnlineOrdersService.getRecentOnlineOrders(parseInt(limit));
    } else {
      // Get all orders
      orders = await OnlineOrdersService.getAllOnlineOrders();
    }

    return NextResponse.json({
      success: true,
      data: orders,
      total: orders.length
    });
  } catch (error) {
    console.error('Error fetching online orders:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch online orders' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, ...orderData } = body;

    if (action === 'sync') {
      // Sync orders from external platform
      const { platformOrders, platform } = body;
      
      if (!platformOrders || !platform) {
        return NextResponse.json(
          { success: false, error: 'Platform orders and platform are required for sync' },
          { status: 400 }
        );
      }

      const result = await OnlineOrdersService.syncOrdersFromPlatform(platformOrders, platform);
      
      return NextResponse.json({
        success: true,
        data: result,
        message: `Synced ${result.synced} orders successfully. ${result.errors} errors.`
      });
    } else {
      // Create new online order
      const requiredFields = ['customer', 'email', 'items', 'total', 'platform', 'originalOrderId'];
      const missingFields = requiredFields.filter(field => !orderData[field]);
      
      if (missingFields.length > 0) {
        return NextResponse.json(
          { success: false, error: `Missing required fields: ${missingFields.join(', ')}` },
          { status: 400 }
        );
      }

      // Set default values
      const newOrder = {
        customer: orderData.customer,
        email: orderData.email,
        phone: orderData.phone || '',
        items: orderData.items,
        total: orderData.total,
        status: orderData.status || 'Pending',
        orderDate: orderData.orderDate || new Date().toISOString(),
        platform: orderData.platform,
        paymentMethod: orderData.paymentMethod || 'Unknown',
        deliveryAddress: orderData.deliveryAddress || '',
        trackingNumber: orderData.trackingNumber,
        financialStatus: orderData.financialStatus || 'pending',
        fulfillmentStatus: orderData.fulfillmentStatus || 'unfulfilled',
        currency: orderData.currency || 'EGP',
        tags: orderData.tags || [],
        notes: orderData.notes,
        source: orderData.source || orderData.platform,
        originalOrderId: orderData.originalOrderId,
        syncedFromPlatform: orderData.syncedFromPlatform || false
      };

      const docId = await OnlineOrdersService.createOrUpdateOnlineOrder(newOrder);

      return NextResponse.json({
        success: true,
        data: { id: docId, ...newOrder },
        message: 'Online order created successfully'
      });
    }
  } catch (error) {
    console.error('Error creating online order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create online order' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, ...updates } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Order ID is required' },
        { status: 400 }
      );
    }

    await OnlineOrdersService.updateOnlineOrder(id, updates);

    return NextResponse.json({
      success: true,
      message: 'Online order updated successfully'
    });
  } catch (error) {
    console.error('Error updating online order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update online order' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Order ID is required' },
        { status: 400 }
      );
    }

    await OnlineOrdersService.deleteOnlineOrder(id);

    return NextResponse.json({
      success: true,
      message: 'Online order deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting online order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete online order' },
      { status: 500 }
    );
  }
}

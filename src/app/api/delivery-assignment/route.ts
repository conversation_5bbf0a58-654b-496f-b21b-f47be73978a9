import { NextRequest, NextResponse } from 'next/server';
import { DeliveryAssignmentService } from '@/services/firebase/deliveryAssignment';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const orderId = searchParams.get('orderId');

    if (action === 'available-agents') {
      // Get available delivery agents
      const agents = await DeliveryAssignmentService.getAvailableDeliveryAgents();
      return NextResponse.json({
        success: true,
        data: agents
      });
    } else if (action === 'workload') {
      // Get delivery agent workload summary
      const workload = await DeliveryAssignmentService.getDeliveryAgentWorkload();
      return NextResponse.json({
        success: true,
        data: workload
      });
    } else if (action === 'auto-assign' && orderId) {
      // Auto-assign delivery agent to specific order
      const success = await DeliveryAssignmentService.assignDeliveryAgentToOrder(orderId);
      return NextResponse.json({
        success,
        message: success ? 'Delivery agent assigned successfully' : 'Failed to assign delivery agent'
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid action or missing parameters' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error in delivery assignment GET:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process delivery assignment request' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, orderId, agentName } = body;

    if (action === 'assign') {
      // Assign specific delivery agent to order
      if (!orderId) {
        return NextResponse.json(
          { success: false, error: 'Order ID is required' },
          { status: 400 }
        );
      }

      const success = await DeliveryAssignmentService.assignDeliveryAgentToOrder(orderId, agentName);
      
      return NextResponse.json({
        success,
        message: success ? 'Delivery agent assigned successfully' : 'Failed to assign delivery agent'
      });
    } else if (action === 'reassign') {
      // Reassign delivery agent
      if (!orderId || !agentName) {
        return NextResponse.json(
          { success: false, error: 'Order ID and agent name are required for reassignment' },
          { status: 400 }
        );
      }

      const success = await DeliveryAssignmentService.reassignDeliveryAgent(orderId, agentName);
      
      return NextResponse.json({
        success,
        message: success ? 'Delivery agent reassigned successfully' : 'Failed to reassign delivery agent'
      });
    } else if (action === 'auto-assign') {
      // Auto-assign delivery agent
      if (!orderId) {
        return NextResponse.json(
          { success: false, error: 'Order ID is required' },
          { status: 400 }
        );
      }

      const success = await DeliveryAssignmentService.assignDeliveryAgentToOrder(orderId);
      
      return NextResponse.json({
        success,
        message: success ? 'Delivery agent auto-assigned successfully' : 'Failed to auto-assign delivery agent'
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid action' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error in delivery assignment POST:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process delivery assignment request' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { orderId, agentName } = body;

    if (!orderId || !agentName) {
      return NextResponse.json(
        { success: false, error: 'Order ID and agent name are required' },
        { status: 400 }
      );
    }

    const success = await DeliveryAssignmentService.reassignDeliveryAgent(orderId, agentName);
    
    return NextResponse.json({
      success,
      message: success ? 'Delivery agent reassigned successfully' : 'Failed to reassign delivery agent'
    });
  } catch (error) {
    console.error('Error in delivery assignment PUT:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to reassign delivery agent' },
      { status: 500 }
    );
  }
}

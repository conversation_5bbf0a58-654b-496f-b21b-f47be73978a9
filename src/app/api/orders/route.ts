import { NextRequest, NextResponse } from 'next/server';
import { OrdersService, Order } from '@/services/firebase/orders';
import { EmailService } from '@/services/email/emailService';
import { DeliveryAssignmentService } from '@/services/firebase/deliveryAssignment';
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const tab = searchParams.get('tab');
    const limit = searchParams.get('limit');

    let orders;

    // Filter by specific status if provided
    if (status && status !== 'all') {
      orders = await OrdersService.getOrdersByStatus(status);
    } else if (tab) {
      // Filter by tab type
      const statusMapping: Record<string, string[]> = {
        oncoming: ['Pending'],
        ongoing: ['Started', 'Moved to Supplier', 'Arrived at Supplier', 'Moving to Customer', 'Arrived at Customer'],
        completed: ['Delivered'],
        cancelled: ['Cancelled']
      };

      const allowedStatuses = statusMapping[tab];
      if (allowedStatuses) {
        // Get orders for each status in the tab
        const statusPromises = allowedStatuses.map(status =>
          OrdersService.getOrdersByStatus(status)
        );
        const statusResults = await Promise.all(statusPromises);
        orders = statusResults.flat();
      } else {
        orders = await OrdersService.getAllOrders();
      }
    } else if (limit) {
      // Get recent orders with limit
      const limitNum = parseInt(limit);
      orders = await OrdersService.getRecentOrders(limitNum);
    } else {
      // Get all orders
      orders = await OrdersService.getAllOrders();
    }

    // Convert Firestore data to match the expected format
    const formattedOrders = orders.map(order => ({
      id: order.orderId,
      customer: order.customerName,
      avatar: null,
      status: order.status,
      date: order.createdAt.toDate().toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }),
      amount: `${order.totalAmount} EGP`,
      address: order.customerAddress,
      phone: order.customerPhone,
      items: order.items || [],
      actionLog: order.actions?.map(action => ({
        id: `action-${Date.now()}-${Math.random()}`,
        timestamp: action.timestamp.toDate().toLocaleString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        }),
        status: action.action,
        location: action.location ?
          (action.location.address || `${action.location.lat.toFixed(4)}, ${action.location.lng.toFixed(4)}`) :
          'Unknown Location',
        gpsCoordinates: action.location || { lat: 0, lng: 0 },
        userId: 'system',
        userName: action.user,
        notes: ''
      })) || [],

      salesRep: order.salesRep,
      estimatedDeliveryTime: order.estimatedDeliveryTime,
      depositAmount: order.depositAmount ? `${order.depositAmount} EGP` : undefined,
      deliveryAgent: order.deliveryAgent,
      cancellationReason: order.cancellationReason,
      purchasingRep: order.purchasingRep,
      driverId: order.driverId,
      driverName: order.driverName,
      truckId: order.truckId,
      truckLicensePlate: order.truckLicensePlate
    }));

    return NextResponse.json({
      success: true,
      data: formattedOrders,
      total: formattedOrders.length
    });
  } catch (error) {
    console.error('Error fetching orders:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Creating order with data:', body);

    // Validate required fields
    const { customerName, customerAddress, customerPhone, items, totalAmount, orderType } = body;

    if (!customerName || !customerAddress || !customerPhone || items === undefined || !totalAmount || !orderType) {
      console.log('Validation failed:', {
        customerName: !!customerName,
        customerAddress: !!customerAddress,
        customerPhone: !!customerPhone,
        items: items !== undefined,
        totalAmount: !!totalAmount,
        orderType: !!orderType
      });
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Generate new order ID
    const allOrders = await OrdersService.getAllOrders();
    const newOrderNumber = allOrders.length + 1;
    const newOrderId = `ORD-${String(newOrderNumber).padStart(3, '0')}`;

    // Create order object with proper typing
    const orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'> = {
      orderId: newOrderId,
      customerName,
      customerAddress,
      customerPhone,
      orderType,
      totalAmount,
      items,
      status: 'Pending' as const,
      salesRep: body.salesRep || 'System',
      depositAmount: body.depositAmount || 0
    };

    // Only add optional fields if they have values
    if (body.estimatedDeliveryTime) orderData.estimatedDeliveryTime = body.estimatedDeliveryTime;
    if (body.deliveryAgent) orderData.deliveryAgent = body.deliveryAgent;
    if (body.purchasingRep) orderData.purchasingRep = body.purchasingRep;
    if (body.driverId) orderData.driverId = body.driverId;
    if (body.driverName) orderData.driverName = body.driverName;
    if (body.truckId) orderData.truckId = body.truckId;
    if (body.truckLicensePlate) orderData.truckLicensePlate = body.truckLicensePlate;

    const newOrder = orderData;

    console.log('Creating order with data:', newOrder);
    const docId = await OrdersService.createOrder(newOrder);
    console.log('Order created with ID:', docId);

    // Add initial action with "Pending" status using user information from request
    const createdBy = body.createdBy || newOrder.salesRep;
    const creationLocation = body.creationLocation || { lat: 30.0444, lng: 31.2357 };

    await OrdersService.addOrderAction(
      docId,
      'Pending',
      createdBy,
      creationLocation
    );
    console.log('Initial action added to order');

    return NextResponse.json({
      success: true,
      data: { id: docId, ...newOrder },
      message: 'Order created successfully'
    });
  } catch (error) {
    console.error('Error creating order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create order' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, status, actionLog, deliveryAgent, cancellationReason, ...otherUpdates } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Find order by orderId field (e.g., "ORD-001") instead of document ID
    const orderResult = await OrdersService.getOrderByOrderId(id);
    if (!orderResult) {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      );
    }

    const { order: existingOrder, docId } = orderResult;

    // Prepare updates object
    const updates: Record<string, unknown> = { ...otherUpdates };

    if (status) updates.status = status;
    if (deliveryAgent) updates.deliveryAgent = deliveryAgent;
    if (cancellationReason) updates.cancellationReason = cancellationReason;

    // Update the order using the document ID
    await OrdersService.updateOrder(docId, updates);

    // Add action log entry if provided, otherwise add automatic status change entry
    if (actionLog) {
      // Use the status field from actionLog as the action text (frontend sends the new status here)
      await OrdersService.addOrderAction(
        docId,
        actionLog.status || actionLog.action || status,
        actionLog.userName || 'System',
        actionLog.gpsCoordinates || { lat: 30.0444, lng: 31.2357 }
      );
    } else if (status && status !== existingOrder.status) {
      // Only add automatic status change entry if no action log was provided
      await OrdersService.addOrderAction(
        docId,
        status,
        body.user || 'System',
        body.location || { lat: 30.0444, lng: 31.2357 }
      );
    }

    // Auto-assign delivery agent when order is started (if not already assigned)
    if (status === 'Started' && !existingOrder.deliveryAgent) {
      try {
        const assignmentSuccess = await DeliveryAssignmentService.assignDeliveryAgentToOrder(existingOrder.orderId);
        if (assignmentSuccess) {
          console.log(`Auto-assigned delivery agent to order ${existingOrder.orderId} when started`);
        }
      } catch (assignmentError) {
        console.error('Error auto-assigning delivery agent:', assignmentError);
        // Don't fail the status update if assignment fails
      }
    }

    const updatedOrder = await OrdersService.getOrder(docId);

    // Send notifications if order status changed
    if (updatedOrder && status && status !== existingOrder.status) {
      try {
        // Send email notification for delivered/cancelled orders
        if (status === 'Delivered' || status === 'Cancelled') {
          await EmailService.sendOrderNotification({
            order: updatedOrder,
            recipientEmail: '<EMAIL>',
            type: status === 'Delivered' ? 'delivered' : 'cancelled'
          });
        }
      } catch (notificationError) {
        console.error('Failed to send email notification:', notificationError);
        // Don't fail the order update if email notification fails
      }
    }

    // Format the updated order to match frontend expectations
    const formattedOrder = {
      id: updatedOrder?.orderId,
      customer: updatedOrder?.customerName,
      avatar: null,
      status: updatedOrder?.status,
      date: updatedOrder?.createdAt.toDate().toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }),
      amount: `${updatedOrder?.totalAmount} EGP`,
      address: updatedOrder?.customerAddress,
      phone: updatedOrder?.customerPhone,
      items: updatedOrder?.items || [],
      actionLog: updatedOrder?.actions?.map(action => ({
        id: `action-${Date.now()}-${Math.random()}`,
        timestamp: action.timestamp.toDate().toLocaleString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        }),
        status: action.action,
        location: action.location ?
          (action.location.address || `${action.location.lat.toFixed(4)}, ${action.location.lng.toFixed(4)}`) :
          'Unknown Location',
        gpsCoordinates: action.location || { lat: 0, lng: 0 },
        userId: 'system',
        userName: action.user,
        notes: ''
      })) || [],
      salesRep: updatedOrder?.salesRep,
      estimatedDeliveryTime: updatedOrder?.estimatedDeliveryTime,
      depositAmount: updatedOrder?.depositAmount ? `${updatedOrder.depositAmount} EGP` : undefined,
      deliveryAgent: updatedOrder?.deliveryAgent,
      cancellationReason: updatedOrder?.cancellationReason,
      purchasingRep: updatedOrder?.purchasingRep,
      driverId: updatedOrder?.driverId,
      driverName: updatedOrder?.driverName,
      truckId: updatedOrder?.truckId,
      truckLicensePlate: updatedOrder?.truckLicensePlate
    };

    return NextResponse.json({
      success: true,
      data: formattedOrder,
      message: 'Order updated successfully'
    });
  } catch (error) {
    console.error('Error updating order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update order' },
      { status: 500 }
    );
  }
}

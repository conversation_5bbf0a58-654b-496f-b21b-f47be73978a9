import { NextRequest, NextResponse } from 'next/server';
import { ApprovedOrdersService, ApprovedOrder } from '@/services/firebase/approvedOrders';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = searchParams.get('limit');

    let orders: ApprovedOrder[];

    // Filter by specific status if provided
    if (status && status !== 'all') {
      orders = await ApprovedOrdersService.getApprovedOrdersByStatus(status);
    } else if (limit) {
      // Get recent orders with limit
      orders = await ApprovedOrdersService.getRecentApprovedOrders(parseInt(limit));
    } else {
      // Get all orders
      orders = await ApprovedOrdersService.getAllApprovedOrders();
    }

    return NextResponse.json({
      success: true,
      data: orders,
      total: orders.length
    });
  } catch (error) {
    console.error('Error fetching approved orders:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch approved orders' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, ...orderData } = body;

    if (action === 'approve') {
      // Approve an online order and move it to approved orders
      const { onlineOrderId, approvedBy } = body;
      
      if (!onlineOrderId) {
        return NextResponse.json(
          { success: false, error: 'Online order ID is required for approval' },
          { status: 400 }
        );
      }

      // This would typically fetch the online order and convert it
      // For now, we'll expect the full order data in the request
      const approvedOrder = {
        ...orderData,
        status: 'Approved' as const,
        approvedDate: new Date().toISOString(),
        approvedBy: approvedBy || 'Unknown'
      };

      const docId = await ApprovedOrdersService.createApprovedOrder(approvedOrder);

      return NextResponse.json({
        success: true,
        data: { id: docId, ...approvedOrder },
        message: 'Order approved successfully'
      });
    } else if (action === 'move-to-main') {
      // Move approved order to main orders system
      const { id, userId } = body;
      
      if (!id) {
        return NextResponse.json(
          { success: false, error: 'Approved order ID is required' },
          { status: 400 }
        );
      }

      const success = await ApprovedOrdersService.moveToMainOrders(id, userId);
      
      if (success) {
        return NextResponse.json({
          success: true,
          message: 'Order moved to main orders successfully'
        });
      } else {
        return NextResponse.json(
          { success: false, error: 'Failed to move order to main orders' },
          { status: 500 }
        );
      }
    } else {
      // Create new approved order
      const requiredFields = ['customer', 'email', 'items', 'total', 'originalOrderId'];
      const missingFields = requiredFields.filter(field => !orderData[field]);
      
      if (missingFields.length > 0) {
        return NextResponse.json(
          { success: false, error: `Missing required fields: ${missingFields.join(', ')}` },
          { status: 400 }
        );
      }

      // Set default values
      const newApprovedOrder = {
        customer: orderData.customer,
        email: orderData.email,
        phone: orderData.phone || '',
        items: orderData.items,
        total: orderData.total,
        status: orderData.status || 'Pending',
        orderDate: orderData.orderDate || new Date().toISOString(),
        platform: orderData.platform || 'Unknown',
        paymentMethod: orderData.paymentMethod || 'Unknown',
        deliveryAddress: orderData.deliveryAddress || '',
        financialStatus: orderData.financialStatus || 'pending',
        fulfillmentStatus: orderData.fulfillmentStatus || 'unfulfilled',
        currency: orderData.currency || 'EGP',
        tags: orderData.tags || [],
        notes: orderData.notes,
        source: orderData.source,
        approvedDate: orderData.approvedDate || new Date().toISOString(),
        originalOrderId: orderData.originalOrderId,
        approvedBy: orderData.approvedBy
      };

      const docId = await ApprovedOrdersService.createApprovedOrder(newApprovedOrder);

      return NextResponse.json({
        success: true,
        data: { id: docId, ...newApprovedOrder },
        message: 'Approved order created successfully'
      });
    }
  } catch (error) {
    console.error('Error processing approved order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process approved order' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, action, ...updates } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Order ID is required' },
        { status: 400 }
      );
    }

    if (action === 'cancel') {
      // Cancel approved order with reason
      const { reason, notes, cancelledBy } = body;
      
      if (!reason) {
        return NextResponse.json(
          { success: false, error: 'Cancellation reason is required' },
          { status: 400 }
        );
      }

      await ApprovedOrdersService.cancelApprovedOrder(id, reason, notes, cancelledBy);

      return NextResponse.json({
        success: true,
        message: 'Approved order cancelled successfully'
      });
    } else if (action === 'update-status') {
      // Update order status
      const { status, updatedBy } = body;
      
      if (!status) {
        return NextResponse.json(
          { success: false, error: 'Status is required' },
          { status: 400 }
        );
      }

      await ApprovedOrdersService.updateApprovedOrderStatus(id, status, updatedBy);

      return NextResponse.json({
        success: true,
        message: 'Approved order status updated successfully'
      });
    } else {
      // General update
      await ApprovedOrdersService.updateApprovedOrder(id, updates);

      return NextResponse.json({
        success: true,
        message: 'Approved order updated successfully'
      });
    }
  } catch (error) {
    console.error('Error updating approved order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update approved order' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Order ID is required' },
        { status: 400 }
      );
    }

    await ApprovedOrdersService.deleteApprovedOrder(id);

    return NextResponse.json({
      success: true,
      message: 'Approved order deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting approved order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete approved order' },
      { status: 500 }
    );
  }
}

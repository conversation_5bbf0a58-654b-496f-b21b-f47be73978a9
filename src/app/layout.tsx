import type { Metadata } from "next";
import "./globals.css";
import FontOptimizer from "@/components/FontOptimizer";

export const metadata: Metadata = {
  title: "ZAWAYA - Delivery Management System",
  description: "Comprehensive delivery management platform for tracking, managing, and optimizing logistics operations",
  keywords: ["delivery", "logistics", "management", "tracking", "ZAWAYA"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html suppressHydrationWarning>
      <head>
        {process.env.NODE_ENV === 'development' && (
          <script
            dangerouslySetInnerHTML={{
              __html: `
                (function() {
                  const original = {
                    log: console.log,
                    warn: console.warn,
                    error: console.error
                  };

                  const patterns = [
                    'preloaded with link preload was not used',
                    'unreachable code after return statement',
                    'Fast Refresh',
                    'The resource at',
                    'preload tag are set correctly',
                    'rebuilding',
                    'done in',
                    'node_modules_',
                    'woff2'
                  ];

                  function filter(args) {
                    const msg = args.join(' ').toLowerCase();
                    return patterns.some(p => msg.includes(p.toLowerCase()));
                  }

                  console.log = (...args) => !filter(args) && original.log(...args);
                  console.warn = (...args) => !filter(args) && original.warn(...args);
                  console.error = (...args) => !filter(args) && original.error(...args);
                })();
              `,
            }}
          />
        )}
      </head>
      <body className="antialiased" suppressHydrationWarning>
        <FontOptimizer />
        {children}
      </body>
    </html>
  );
}

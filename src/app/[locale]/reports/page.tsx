'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useReportsTranslations, useCommonTranslations, createDynamicKey } from '@/hooks/useTranslations';
import ReportsService, { ReportConfig, GeneratedReport, DataSource, ChartType, TimePeriod } from '@/services/ReportsService';
import { Timestamp } from 'firebase/firestore';
import {
  TrendingUp,
  BarChart3,
  FileText,
  Download,
  Calendar,
  Search,
  Plus,
  Settings,
  Database,
  LineChart,
  Grid,
  Save,
  Play,
  BarChart,
  PieChart,
  Table,
  Users,
  Package,
  Clock,
  Target,
  Edit,
  Trash2
} from 'lucide-react';
import { PageTransition } from '@/components/PageTransition';
import { useNavigationTransition } from '@/hooks/useNavigationTransition';
import { Sidebar } from '@/components/Sidebar';
import { Header } from '@/components/Header';
import { SettingsModal } from '@/components/SettingsModal';
import {
  CompactModal,
  TwoColumnForm,
  FormField,
  IconInput,
  IconSelect,
  ModalActions,
  FullWidthField
} from '@/components/CompactModal';
import { useConfirmationDialog } from '@/components/ui/ConfirmationDialog';

// Data source and chart type configurations remain the same

// Data source configuration
const dataSourceConfig = {
  orders: {
    icon: Package,
    color: 'bg-blue-100 text-blue-800',
    fields: ['orderId', 'customerName', 'orderType', 'status', 'totalAmount', 'createdAt', 'deliveryAgent'],
    aggregateFields: ['totalAmount', 'deliveryTime']
  },
  customers: {
    icon: Users,
    color: 'bg-green-100 text-green-800',
    fields: ['name', 'email', 'phone', 'status', 'isVIP', 'totalOrders', 'totalSpent', 'joinDate'],
    aggregateFields: ['totalOrders', 'totalSpent']
  },
  deliveries: {
    icon: Clock,
    color: 'bg-orange-100 text-orange-800',
    fields: ['orderId', 'driver', 'status', 'eta', 'priority', 'createdAt'],
    aggregateFields: ['deliveryTime']
  },
  team: {
    icon: Users,
    color: 'bg-purple-100 text-purple-800',
    fields: ['name', 'role', 'status', 'phone', 'email', 'joinDate'],
    aggregateFields: ['ordersHandled']
  },
  fleet: {
    icon: Target,
    color: 'bg-red-100 text-red-800',
    fields: ['plateNumber', 'model', 'status', 'driver', 'capacity'],
    aggregateFields: ['deliveriesCompleted']
  }
};

// Chart type configuration
const chartTypeConfig = {
  bar: { icon: BarChart, label: 'Bar Chart' },
  line: { icon: LineChart, label: 'Line Chart' },
  pie: { icon: PieChart, label: 'Pie Chart' },
  table: { icon: Table, label: 'Data Table' },
  metric: { icon: TrendingUp, label: 'Metric Card' }
};

// Report Builder Modal Component
const ReportBuilderModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSave: (config: Omit<ReportConfig, 'id' | 'createdAt' | 'userId'>) => void;
  editConfig?: ReportConfig | null;
}> = ({ isOpen, onClose, onSave, editConfig }) => {
  const t = useReportsTranslations();
  const common = useCommonTranslations();

  const [name, setName] = useState('');
  const [dataSource, setDataSource] = useState<DataSource>('orders');
  const [chartType, setChartType] = useState<ChartType>('table');
  const [period, setPeriod] = useState<TimePeriod>('30d');
  const [groupBy, setGroupBy] = useState('');
  const [aggregation, setAggregation] = useState<'count' | 'sum' | 'avg' | 'min' | 'max'>('count');
  const [field, setField] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if (editConfig) {
        setName(editConfig.name);
        setDataSource(editConfig.dataSource);
        setChartType(editConfig.chartType);
        setPeriod(editConfig.period);
        setGroupBy(editConfig.groupBy || '');
        setAggregation(editConfig.aggregation || 'count');
        setField(editConfig.field || '');
      } else {
        // Reset form for new report
        setName('');
        setDataSource('orders');
        setChartType('table');
        setPeriod('30d');
        setGroupBy('');
        setAggregation('count');
        setField('');
      }
      setLoading(false);
    }
  }, [isOpen, editConfig]);

  const handleSave = async () => {
    if (!name) return;

    setLoading(true);
    try {
      const config: Omit<ReportConfig, 'id' | 'createdAt' | 'userId'> = {
        name,
        dataSource,
        chartType,
        period,
        filters: {}
      };

      // Only add optional fields if they have values
      if (groupBy) {
        config.groupBy = groupBy;
      }
      if (aggregation && aggregation !== 'count') {
        config.aggregation = aggregation;
      }
      if (field) {
        config.field = field;
      }

      await onSave(config);
      onClose();
    } catch (error) {
      console.error('Error saving report:', error);
    } finally {
      setLoading(false);
    }
  };

  const currentDataSource = dataSourceConfig[dataSource];
  const availableFields = currentDataSource.fields;
  const availableAggregateFields = currentDataSource.aggregateFields;

  return (
    <CompactModal
      isOpen={isOpen}
      onClose={onClose}
      title={editConfig ? t('editCustomReport') : t('createCustomReport')}
      maxWidth="max-w-2xl"
    >
      <form
        onSubmit={(e) => {
          e.preventDefault();
          handleSave();
        }}
        className="space-y-4"
      >
        <TwoColumnForm>
          <FullWidthField>
            <FormField label={`${t('reportName')} *`}>
              <IconInput
                icon={<FileText className="w-4 h-4" />}
                type="text"
                required
                value={name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setName(e.target.value)}
                placeholder={t('enterReportName')}
              />
            </FormField>
          </FullWidthField>

          <FormField label={t('dataSource')}>
            <IconSelect
              icon={<Database className="w-4 h-4" />}
              value={dataSource}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setDataSource(e.target.value as DataSource)}
            >
              <option value="orders">{t('types.orders')}</option>
              <option value="customers">{t('types.customers')}</option>
              <option value="deliveries">{t('deliveries')}</option>
              <option value="team">{t('teamMembers')}</option>
              <option value="fleet">{t('fleet')}</option>
            </IconSelect>
          </FormField>

          <FormField label={t('chartType')}>
            <IconSelect
              icon={<BarChart3 className="w-4 h-4" />}
              value={chartType}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setChartType(e.target.value as ChartType)}
            >
              <option value="table">{t('dataTable')}</option>
              <option value="bar">{t('barChart')}</option>
              <option value="line">{t('lineChart')}</option>
              <option value="pie">{t('pieChart')}</option>
              <option value="metric">{t('metricCard')}</option>
            </IconSelect>
          </FormField>

          <FormField label={t('timePeriod')}>
            <IconSelect
              icon={<Calendar className="w-4 h-4" />}
              value={period}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setPeriod(e.target.value as TimePeriod)}
            >
              <option value="7d">{t('last7Days')}</option>
              <option value="30d">{t('last30Days')}</option>
              <option value="3m">{t('last3Months')}</option>
              <option value="6m">{t('last6Months')}</option>
              <option value="1y">{t('lastYear')}</option>
              <option value="all">{t('allTime')}</option>
            </IconSelect>
          </FormField>

          <FormField label={t('groupBy')}>
            <IconSelect
              icon={<Grid className="w-4 h-4" />}
              value={groupBy}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setGroupBy(e.target.value)}
            >
              <option value="">{t('noGrouping')}</option>
              {availableFields.map(field => (
                <option key={field} value={field}>{t(createDynamicKey(`fields.${field}`)) || field}</option>
              ))}
            </IconSelect>
          </FormField>

          {chartType !== 'table' && (
            <>
              <FormField label={t('aggregation')}>
                <IconSelect
                  icon={<TrendingUp className="w-4 h-4" />}
                  value={aggregation}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setAggregation(e.target.value as 'count' | 'sum' | 'avg' | 'min' | 'max')}
                >
                  <option value="count">{t('count')}</option>
                  <option value="sum">{t('sum')}</option>
                  <option value="avg">{t('average')}</option>
                  <option value="min">{t('minimum')}</option>
                  <option value="max">{t('maximum')}</option>
                </IconSelect>
              </FormField>

              {aggregation !== 'count' && (
                <FormField label={t('field')}>
                  <IconSelect
                    icon={<Settings className="w-4 h-4" />}
                    value={field}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setField(e.target.value)}
                  >
                    <option value="">{t('selectField')}</option>
                    {availableAggregateFields.map(field => (
                      <option key={field} value={field}>{t(createDynamicKey(`fields.${field}`)) || field}</option>
                    ))}
                  </IconSelect>
                </FormField>
              )}
            </>
          )}
        </TwoColumnForm>

        <ModalActions
          onCancel={onClose}
          cancelText={common('cancel')}
          submitText={loading ? `${common('loading')}...` : (editConfig ? t('updateReport') : t('createReport'))}
          icon={<Save className="w-4 h-4" />}
          showCancel={false}
        />
      </form>
    </CompactModal>
  );
};

// Report Card Component
const ReportCard: React.FC<{
  config: ReportConfig;
  onEdit: (config: ReportConfig) => void;
  onRun: (config: ReportConfig) => void;
  onDelete: (id: string) => void;
}> = ({ config, onEdit, onRun, onDelete }) => {
  const t = useReportsTranslations();
  const sourceConfig = dataSourceConfig[config.dataSource];
  const chartConfig = chartTypeConfig[config.chartType];
  const IconComponent = sourceConfig.icon;
  const ChartIcon = chartConfig.icon;

  return (
    <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${sourceConfig.color}`}>
            <IconComponent className="w-6 h-6" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{config.name}</h3>
            <p className="text-sm text-gray-500">
              {t(createDynamicKey(`types.${config.dataSource}`))} • {t(createDynamicKey(`${config.chartType === 'table' ? 'dataTable' : config.chartType + 'Chart'}`))}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => onEdit(config)}
            className="text-gray-400 hover:text-indigo-600 transition-colors"
            title="Edit Report"
          >
            <Edit className="w-4 h-4" />
          </button>
          <button
            onClick={() => onDelete(config.id)}
            className="text-gray-400 hover:text-red-600 transition-colors"
            title="Delete Report"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>

      <div className="space-y-3 mb-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Calendar className="w-4 h-4" />
          <span>{t('period')}: {config.period}</span>
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <ChartIcon className="w-4 h-4" />
          <span>{t('type')}: {t(createDynamicKey(`${config.chartType === 'table' ? 'dataTable' : config.chartType + 'Chart'}`))}
          </span>
        </div>
        {config.groupBy && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Grid className="w-4 h-4" />
            <span>{t('groupedBy')}: {t(createDynamicKey(`fields.${config.groupBy}`))}
            </span>
          </div>
        )}
        {config.lastRun && (
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>{t('lastRun')}: {config.lastRun instanceof Timestamp ? config.lastRun.toDate().toLocaleDateString() : new Date(config.lastRun).toLocaleDateString()}</span>
          </div>
        )}
      </div>

      <button
        onClick={() => onRun(config)}
        className="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors flex items-center justify-center gap-2"
      >
        <Play className="w-4 h-4" />
        {t('runReport')}
      </button>
    </div>
  );
};

// Report Results Modal
const ReportResultsModal: React.FC<{
  report: GeneratedReport | null;
  isOpen: boolean;
  onClose: () => void;
}> = ({ report, isOpen, onClose }) => {
  const t = useReportsTranslations();
  if (!isOpen || !report) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto mx-4">
        <div className="p-4 sm:p-6 border-b border-gray-100">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{report.config.name}</h2>
              <p className="text-sm text-gray-500">Generated on {report.generatedAt instanceof Timestamp ? report.generatedAt.toDate().toLocaleDateString() : new Date(report.generatedAt).toLocaleDateString()}</p>
            </div>
            <button
              onClick={() => {
                // Export functionality
                console.log('Exporting report:', report.id);
              }}
              className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              {t('export')}
            </button>
          </div>
        </div>

        <div className="p-4 sm:p-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm font-medium text-gray-600">{t('records')}</p>
              <p className="text-2xl font-bold text-gray-900">{report.summary.totalRecords}</p>
            </div>
            {report.summary.totalValue && (
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm font-medium text-gray-600">{t('totalValue')}</p>
                <p className="text-2xl font-bold text-gray-900">{report.summary.totalValue.toLocaleString()} EGP</p>
              </div>
            )}
            {report.summary.averageValue && (
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm font-medium text-gray-600">{t('averageValue')}</p>
                <p className="text-2xl font-bold text-gray-900">{report.summary.averageValue.toLocaleString()} EGP</p>
              </div>
            )}
          </div>

          {/* Data Display */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('reportData')}</h3>
            {report.config.chartType === 'table' ? (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      {report.data.length > 0 && Object.keys(report.data[0]).map(key => (
                        <th key={key} className="text-left py-2 px-4 font-medium text-gray-700">
                          {t(createDynamicKey(`fields.${key}`))}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {report.data.map((row, index) => (
                      <tr key={index} className="border-b border-gray-100">
                        {Object.values(row).map((value, cellIndex) => (
                          <td key={cellIndex} className="py-2 px-4 text-gray-600">
                            {String(value)}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  {React.createElement(chartTypeConfig[report.config.chartType].icon, {
                    className: "w-8 h-8 text-indigo-600"
                  })}
                </div>
                <p className="text-gray-600">{t('chartVisualization')}</p>
                <p className="text-sm text-gray-500 mt-2">
                  {report.data.length} {t('dataPointsFor')} {report.config.chartType} {t('chart')}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Main Reports Page Component
const ReportsPage: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const t = useReportsTranslations();
  const common = useCommonTranslations();

  const [activeItem, setActiveItem] = useState('reports');
  const [reportConfigs, setReportConfigs] = useState<ReportConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const [showBuilderModal, setShowBuilderModal] = useState(false);
  const [showResultsModal, setShowResultsModal] = useState(false);
  const [editingConfig, setEditingConfig] = useState<ReportConfig | null>(null);
  const [currentReport, setCurrentReport] = useState<GeneratedReport | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Navigation transition hook
  const { navigateWithTransition } = useNavigationTransition({ animationType: 'fade' });

  // Confirmation dialog hook
  const { showConfirmation, ConfirmationDialogComponent } = useConfirmationDialog();

  // Handle navigation
  const handleNavigation = (path: string) => {
    navigateWithTransition(path);
  };

  // Load report configurations from Firestore
  useEffect(() => {
    const loadReportConfigs = async () => {
      if (user?.uid) {
        try {
          setLoading(true);
          const configs = await ReportsService.getAllReportConfigs(user.uid);
          setReportConfigs(configs);
        } catch (error) {
          console.error('Error loading report configs:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    loadReportConfigs();
  }, [user?.uid]);

  // Handle save report configuration
  const handleSaveConfig = async (config: Omit<ReportConfig, 'id' | 'createdAt' | 'userId'>) => {
    if (!user?.uid) return;

    try {
      setLoading(true);

      if (editingConfig) {
        // Update existing config
        await ReportsService.updateReportConfig(editingConfig.id, config);
        const updatedConfigs = reportConfigs.map(c =>
          c.id === editingConfig.id ? { ...c, ...config } : c
        );
        setReportConfigs(updatedConfigs);
      } else {
        // Add new config
        const configWithUser = { ...config, userId: user.uid };
        const newId = await ReportsService.createReportConfig(configWithUser);
        const newConfig: ReportConfig = {
          ...configWithUser,
          id: newId,
          createdAt: Timestamp.now()
        };
        setReportConfigs([newConfig, ...reportConfigs]);
      }

      setEditingConfig(null);
      setShowBuilderModal(false);
    } catch (error) {
      console.error('Error saving report config:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle edit report
  const handleEditReport = (config: ReportConfig) => {
    setEditingConfig(config);
    setShowBuilderModal(true);
  };

  // Handle delete report
  const handleDeleteReport = async (id: string) => {
    const reportToDelete = reportConfigs.find(r => r.id === id);
    if (!reportToDelete) return;

    showConfirmation({
      title: t('deleteReport'),
      message: `${t('deleteReportConfirmation').replace('{reportName}', reportToDelete.name)}`,
      confirmText: t('deleteReport'),
      cancelText: common('cancel'),
      type: 'danger',
      onConfirm: async () => {
        try {
          setLoading(true);
          await ReportsService.deleteReportConfig(id);
          const updatedConfigs = reportConfigs.filter(c => c.id !== id);
          setReportConfigs(updatedConfigs);
        } catch (error) {
          console.error('Error deleting report config:', error);
        } finally {
          setLoading(false);
        }
      }
    });
  };

  // Handle run report
  const handleRunReport = async (config: ReportConfig) => {
    if (!user?.uid) return;

    try {
      setLoading(true);

      // Generate report using the service
      const generatedReport = await ReportsService.generateReportFromConfig(config);

      // Update the config in local state with last run time
      const updatedConfigs = reportConfigs.map(c =>
        c.id === config.id ? { ...c, lastRun: Timestamp.now() } : c
      );
      setReportConfigs(updatedConfigs);

      setCurrentReport(generatedReport);
      setShowResultsModal(true);
    } catch (error) {
      console.error('Error running report:', error);
    } finally {
      setLoading(false);
    }
  };

  // Filter reports based on search term
  const filteredConfigs = reportConfigs.filter(config =>
    config.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    config.dataSource.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);

  if (authLoading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  if (!user) {
    return null;
  }

  return (
    <PageTransition transitionKey="reports" animationType="fade">
      <div className="h-screen bg-gray-50 text-gray-900" data-page-content>
        <Sidebar
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
          onNavigate={handleNavigation}
          onOpenSettings={() => setShowSettings(true)}
        />
        <main className="lg:ml-64 flex flex-col h-full overflow-hidden">
          <Header
            showUserMenu={showUserMenu}
            setShowUserMenu={setShowUserMenu}
            setActiveItem={setActiveItem}
            setShowSettings={setShowSettings}
          />
          <div className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-4 sm:p-6 lg:p-8 pt-56">
            {/* Creative Add New Report Section */}
            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl lg:rounded-3xl p-6 sm:p-8 mb-6 lg:mb-8 text-white relative overflow-hidden mt-16">
              <div className="absolute inset-0 bg-black/10"></div>
              <div className="relative z-10">
                <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
                  <div className="flex-1">
                    <h1 className="text-3xl lg:text-4xl font-bold mb-3">{t('customReportBuilder')}</h1>
                    <p className="text-indigo-100 text-lg mb-4">{t('createAndManageReports')}</p>
                    <div className="flex flex-wrap gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <BarChart3 className="w-4 h-4" />
                        <span>{t('multipleChartTypes')}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Database className="w-4 h-4" />
                        <span>{t('realTimeData')}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Download className="w-4 h-4" />
                        <span>{t('exportOptions')}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <button
                      onClick={() => {
                        setEditingConfig(null);
                        setShowBuilderModal(true);
                      }}
                      className="bg-white text-indigo-600 px-8 py-4 rounded-2xl font-semibold hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center gap-3"
                    >
                      <Plus className="w-5 h-5" />
                      {t('createReport')}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 lg:gap-6 mb-6 lg:mb-8">
              <div className="bg-white rounded-2xl p-4 lg:p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('totalReports')}</p>
                    <p className="text-2xl lg:text-3xl font-bold text-gray-900">
                      {reportConfigs.length}
                    </p>
                  </div>
                  <FileText className="w-6 h-6 lg:w-8 lg:h-8 text-indigo-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-4 lg:p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('dataSources')}</p>
                    <p className="text-2xl lg:text-3xl font-bold text-blue-600">
                      {new Set(reportConfigs.map(r => r.dataSource)).size}
                    </p>
                  </div>
                  <Database className="w-6 h-6 lg:w-8 lg:h-8 text-blue-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-4 lg:p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('chartReports')}</p>
                    <p className="text-2xl lg:text-3xl font-bold text-orange-600">
                      {reportConfigs.filter(r => r.chartType !== 'table').length}
                    </p>
                  </div>
                  <BarChart3 className="w-6 h-6 lg:w-8 lg:h-8 text-orange-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-4 lg:p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('tableReports')}</p>
                    <p className="text-2xl lg:text-3xl font-bold text-green-600">
                      {reportConfigs.filter(r => r.chartType === 'table').length}
                    </p>
                  </div>
                  <Table className="w-6 h-6 lg:w-8 lg:h-8 text-green-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-4 lg:p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('recentRuns')}</p>
                    <p className="text-2xl lg:text-3xl font-bold text-purple-600">
                      {reportConfigs.filter(r => r.lastRun).length}
                    </p>
                  </div>
                  <Play className="w-6 h-6 lg:w-8 lg:h-8 text-purple-600" />
                </div>
              </div>
            </div>

            {/* All Reports Table */}
            <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
              {/* Header */}
              <div className="p-4 sm:p-6 border-b border-gray-100">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                  <div className="flex items-center gap-3">
                    <FileText className="w-6 h-6 text-gray-600" />
                    <h3 className="text-lg font-semibold text-gray-900">{t('customReports')}</h3>
                    <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-sm font-medium">
                      {filteredConfigs.length}
                    </span>
                  </div>
                  <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input
                        type="text"
                        placeholder={t('searchReports')}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent w-full sm:w-64"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Reports Grid/List */}
              {filteredConfigs.length > 0 ? (
                <div className="p-4 sm:p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6">
                    {filteredConfigs.map((config) => (
                      <ReportCard
                        key={config.id}
                        config={config}
                        onEdit={handleEditReport}
                        onRun={handleRunReport}
                        onDelete={handleDeleteReport}
                      />
                    ))}
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center py-12">
                  <BarChart3 className="w-16 h-16 text-gray-300 mb-4" />
                  <p className="text-lg font-medium text-gray-500 mb-2">{t('noReportsFound')}</p>
                  <p className="text-sm text-gray-400 mb-6">
                    {searchTerm ? t('noReportsMatch') : t('createFirstReport')}
                  </p>
                  {!searchTerm && (
                    <button
                      onClick={() => {
                        setEditingConfig(null);
                        setShowBuilderModal(true);
                      }}
                      className="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2"
                    >
                      <Plus className="w-4 h-4" />
                      {t('createYourFirstReport')}
                    </button>
                  )}
                </div>
              )}
            </div>

            {/* Loading Overlay */}
            {loading && (
              <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-40">
                <div className="bg-white rounded-lg p-6 flex items-center gap-3">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
                  <span className="text-gray-700">{t('generatingReport')}</span>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>

      {/* Modals */}
      <ReportBuilderModal
        isOpen={showBuilderModal}
        onClose={() => {
          setShowBuilderModal(false);
          setEditingConfig(null);
        }}
        onSave={handleSaveConfig}
        editConfig={editingConfig}
      />

      <ReportResultsModal
        report={currentReport}
        isOpen={showResultsModal}
        onClose={() => {
          setShowResultsModal(false);
          setCurrentReport(null);
        }}
      />

      <SettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialogComponent />
    </PageTransition>
  );
};

export default ReportsPage;

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { useTeamTranslations, useConfirmationTranslations, useToastTranslations, createDynamicKey } from '@/hooks/useTranslations';
import { useConfirmationDialog } from '@/components/ui/ConfirmationDialog';
import { useToastHelpers } from '@/components/ui/Toast';
import {
  Users,
  Plus,
  Edit2,
  Phone,
  Mail,
  MapPin,
  UserCheck,
  UserX,
  Search,
  X,
  Calendar,
  Eye
} from 'lucide-react';

import { SettingsModal } from '@/components/SettingsModal';
import { PageTransition } from '@/components/PageTransition';
import { useNavigationTransition } from '@/hooks/useNavigationTransition';
import { Sidebar } from '@/components/Sidebar';
import { Header } from '@/components/Header';
import { ResponsiveTable, ResponsiveTableRow, ResponsiveTableCell } from '@/components/ResponsiveTable';

// Team member types
type TeamRole = 'Sales' | 'Purchasing' | 'Delivery' | 'Management' | 'Support';

type TeamMember = {
  id: string;
  name: string;
  role: TeamRole;
  email: string;
  phone: string;
  location: string;
  status: 'Active' | 'Inactive';
  joinDate: string;
  avatar?: string;
};

// Add/Edit Member Modal Component
const AddEditMemberModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSave: (member: Omit<TeamMember, 'id'>) => void;
  editingMember?: TeamMember | null;
}> = ({ isOpen, onClose, onSave, editingMember }) => {
  const t = useTeamTranslations();

  const [formData, setFormData] = useState({
    name: '',
    role: 'Sales' as TeamRole,
    email: '',
    phone: '',
    location: '',
    status: 'Active' as 'Active' | 'Inactive',
    joinDate: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    if (editingMember) {
      setFormData({
        name: editingMember.name,
        role: editingMember.role,
        email: editingMember.email,
        phone: editingMember.phone,
        location: editingMember.location,
        status: editingMember.status,
        joinDate: editingMember.joinDate
      });
    } else {
      setFormData({
        name: '',
        role: 'Sales',
        email: '',
        phone: '',
        location: '',
        status: 'Active',
        joinDate: new Date().toISOString().split('T')[0]
      });
    }
  }, [editingMember, isOpen]);

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-2 sm:p-4 backdrop-blur-sm animate-fadeIn">
      <div className="bg-white rounded-2xl p-0 max-w-xl w-full mx-2 sm:mx-4 max-h-[90vh] overflow-hidden shadow-2xl animate-scaleIn">
        {/* Compact header with gradient background */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 py-3 px-5 text-white rounded-t-2xl">
          <h2 className="text-lg font-bold">
            {editingMember ? t(createDynamicKey('editTeamMember')) : t(createDynamicKey('addNewTeamMember'))}
          </h2>
        </div>

        <div className="p-3 sm:p-5 overflow-y-auto max-h-[calc(90vh-60px)]">
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-3 sm:gap-x-4 gap-y-3 sm:gap-y-4">
              {/* Left column */}
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('name')}</label>
                  <div className="relative">
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="w-full px-3 sm:px-4 py-2.5 sm:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 pl-8 sm:pl-9 text-sm"
                      required
                    />
                    <span className="absolute left-2.5 sm:left-3 top-1/2 -translate-y-1/2 text-gray-400">
                      <Users className="w-4 h-4" />
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('email')}</label>
                  <div className="relative">
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 pl-9 text-sm"
                      required
                    />
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                      <Mail className="w-4 h-4" />
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('phone')}</label>
                  <div className="relative">
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      placeholder="+20-1xx-xxx-xxxx"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 pl-9 text-sm"
                      required
                    />
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                      <Phone className="w-4 h-4" />
                    </span>
                  </div>
                </div>
              </div>

              {/* Right column */}
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('role')}</label>
                  <div className="relative">
                    <select
                      value={formData.role}
                      onChange={(e) => setFormData({ ...formData, role: e.target.value as TeamRole })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 pl-9 appearance-none text-sm"
                    >
                      <option value="Sales">{t('roles.sales')}</option>
                      <option value="Purchasing">{t('roles.purchasing')}</option>
                      <option value="Delivery">{t('roles.delivery')}</option>
                      <option value="Management">{t('roles.management')}</option>
                      <option value="Support">{t('roles.support')}</option>
                    </select>
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                      <UserCheck className="w-4 h-4" />
                    </span>
                    <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('status')}</label>
                  <div className="relative">
                    <select
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value as 'Active' | 'Inactive' })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 pl-9 appearance-none text-sm"
                    >
                      <option value="Active">{t('active')}</option>
                      <option value="Inactive">{t('inactive')}</option>
                    </select>
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                      {formData.status === 'Active' ? 
                        <span className="text-green-500"><UserCheck className="w-4 h-4" /></span> : 
                        <span className="text-red-500"><UserX className="w-4 h-4" /></span>
                      }
                    </span>
                    <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('location')}</label>
                  <div className="relative">
                    <input
                      type="text"
                      value={formData.location}
                      onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                      placeholder="Cairo, Egypt"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 pl-9 text-sm"
                      required
                    />
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                      <MapPin className="w-4 h-4" />
                    </span>
                  </div>
                </div>
              </div>

              {/* Full width date field */}
              <div className="col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">{t('joinDate')}</label>
                <div className="relative">
                  <input
                    type="date"
                    value={formData.joinDate}
                    onChange={(e) => setFormData({ ...formData, joinDate: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 pl-9 text-sm"
                    required
                  />
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                    <Calendar className="w-4 h-4" />
                  </span>
                </div>
              </div>

              {/* Action buttons */}
              <div className="col-span-1 sm:col-span-2 flex justify-center pt-3 mt-2">
                <button
                  type="submit"
                  className="w-full sm:w-auto px-6 sm:px-8 py-2.5 sm:py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg font-medium text-sm hover:from-indigo-700 hover:to-purple-700 transition duration-200 shadow-md hover:shadow-lg flex items-center justify-center gap-2"
                >
                  {editingMember ? <Edit2 className="w-4 h-4" /> : <Plus className="w-4 h-4" />}
                  {editingMember ? t('updateMember') : t('addMember')}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// Team Member Details Modal Component
const TeamMemberDetailsModal: React.FC<{
  member: TeamMember | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (member: TeamMember) => void;
  onDelete: (id: string) => void;
}> = ({ member, isOpen, onClose, onEdit, onDelete }) => {
  const t = useTranslations('team');
  const common = useTranslations('common');

  if (!isOpen || !member) return null;

  const getRoleColor = (role: TeamRole) => {
    const colors = {
      Sales: 'bg-blue-100 text-blue-800',
      Purchasing: 'bg-green-100 text-green-800',
      Delivery: 'bg-purple-100 text-purple-800',
      Management: 'bg-red-100 text-red-800',
      Support: 'bg-yellow-100 text-yellow-800'
    };
    return colors[role];
  };

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
      <div className="bg-white rounded-2xl max-w-2xl w-full shadow-2xl">
        {/* Compact header with gradient background */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 py-3 px-5 text-white rounded-t-2xl flex items-center justify-between">
          <h2 className="text-lg font-semibold">{t('teamMemberDetails')}</h2>
          <button
            onClick={onClose}
            className="text-white hover:text-indigo-200 transition-colors rounded-full hover:bg-white/10 p-1"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-5">
          {/* Two-column layout for all info */}
          <div className="flex items-start gap-5">
            {/* Left column with avatar and name */}
            <div className="flex-shrink-0">
              <div className="w-16 h-16 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-md">
                {member.avatar ? (
                  <Image src={member.avatar} alt={member.name} width={64} height={64} className="w-full h-full rounded-full object-cover" />
                ) : (
                  member.name.charAt(0)
                )}
              </div>
            </div>

            {/* Right column with all details */}
            <div className="flex-1 min-w-0">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 mb-3">
                <div>
                  <h3 className="text-xl font-bold text-gray-900 truncate">{member.name}</h3>
                  <span className={`inline-block px-2 py-0.5 text-xs font-semibold rounded-full ${getRoleColor(member.role)} mt-1`}>
                    {member.role === 'Sales' ? t('roles.sales') :
                     member.role === 'Purchasing' ? t('roles.purchasing') :
                     member.role === 'Delivery' ? t('roles.delivery') :
                     member.role === 'Management' ? t('roles.management') :
                     member.role === 'Support' ? t('roles.support') : member.role}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${member.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    <div className={`w-1.5 h-1.5 rounded-full ${member.status === 'Active' ? 'bg-green-600' : 'bg-red-600'}`}></div>
                    {member.status === 'Active' ? t('active') : t('inactive')}
                  </div>
                </div>
              </div>

              {/* Contact information grid */}
              <div className="grid grid-cols-2 gap-3 border-t border-gray-100 pt-3">
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-gray-400" />
                  <div className="min-w-0">
                    <p className="text-xs text-gray-500">Email</p>
                    <p className="text-sm text-gray-900 truncate">{member.email}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-gray-400" />
                  <div className="min-w-0">
                    <p className="text-xs text-gray-500">{t('phone')}</p>
                    <p className="text-sm text-gray-900 truncate">{member.phone}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-gray-400" />
                  <div className="min-w-0">
                    <p className="text-xs text-gray-500">{t('location')}</p>
                    <p className="text-sm text-gray-900 truncate">{member.location}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <div className="min-w-0">
                    <p className="text-xs text-gray-500">{t('joinDate')}</p>
                    <p className="text-sm text-gray-900">{new Date(member.joinDate).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="flex gap-3 mt-5 pt-3 border-t border-gray-100">
            <button
              onClick={() => onEdit(member)}
              className="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center justify-center gap-2"
            >
              <Edit2 className="w-4 h-4" />
              {t('edit')}
            </button>
            <button
              onClick={() => {
                onDelete(member.id);
                onClose();
              }}
              className="flex-1 bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center justify-center gap-2"
            >
              <UserX className="w-4 h-4" />
              {common('deactivate')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main Team Management Component
export default function TeamPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const t = useTranslations('team');
  const common = useTranslations('common');
  const confirmationT = useConfirmationTranslations();
  const toastT = useToastTranslations();
  const { showConfirmation, ConfirmationDialogComponent } = useConfirmationDialog();
  const { success, error } = useToastHelpers();

  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRole, setSelectedRole] = useState<TeamRole | 'All'>('All');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Navigation state
  const [activeItem, setActiveItem] = useState('team');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  const [showSettings, setShowSettings] = useState(false);
  const [showInactive, setShowInactive] = useState(false); // Filter for inactive members

  // Navigation transition hook
  const { navigateWithTransition } = useNavigationTransition({ animationType: 'fade' });

  // Navigation handler
  const handleNavigation = (path: string) => {
    navigateWithTransition(path);
  };

  // Define table columns for responsive table
  const teamColumns = [
    { key: 'member', label: t('member') },
    { key: 'role', label: t('role'), hideOnMobile: true },
    { key: 'status', label: t('status') },
    { key: 'contact', label: t('contact'), hideOnMobile: true },
    { key: 'location', label: t('location'), hideOnMobile: true },
    { key: 'joinDate', label: t('joinDate'), hideOnMobile: true },
    { key: 'actions', label: t('actions'), hideOnMobile: true }
  ];

  // Render desktop table row
  const renderTeamRow = (member: TeamMember) => {
    const handleRowClick = () => {
      handleMemberClick(member);
    };

    return (
      <ResponsiveTableRow
        key={member.id}
        onClick={handleRowClick}
        className="cursor-pointer"
      >
        <ResponsiveTableCell>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
              {member.avatar ? (
                <Image src={member.avatar} alt={member.name} width={40} height={40} className="w-full h-full rounded-full object-cover" />
              ) : (
                member.name.charAt(0)
              )}
            </div>
            <div>
              <p className="text-sm font-semibold text-gray-900 group-hover:text-indigo-600">{member.name}</p>
              <p className="text-xs text-gray-500">{member.email}</p>
            </div>
          </div>
        </ResponsiveTableCell>
        <ResponsiveTableCell>
          <span className={`px-3 py-1 text-xs font-semibold rounded-full ${getRoleColor(member.role)} group-hover:shadow-sm transition-shadow`}>
            {member.role === 'Sales' ? t('roles.sales') :
             member.role === 'Purchasing' ? t('roles.purchasing') :
             member.role === 'Delivery' ? t('roles.delivery') :
             member.role === 'Management' ? t('roles.management') :
             member.role === 'Support' ? t('roles.support') : member.role}
          </span>
        </ResponsiveTableCell>
        <ResponsiveTableCell>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${member.status === 'Active' ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className={`text-xs font-medium ${member.status === 'Active' ? 'text-green-600' : 'text-red-600'}`}>
              {member.status === 'Active' ? t('active') : t('inactive')}
            </span>
          </div>
        </ResponsiveTableCell>
        <ResponsiveTableCell className="text-gray-500">
          {member.phone}
        </ResponsiveTableCell>
        <ResponsiveTableCell className="text-gray-500">
          {member.location}
        </ResponsiveTableCell>
        <ResponsiveTableCell className="text-gray-500">
          {new Date(member.joinDate).toLocaleDateString()}
        </ResponsiveTableCell>
        <ResponsiveTableCell>
          <div className="action-buttons flex items-center gap-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleMemberClick(member);
              }}
              className="text-indigo-600 hover:text-indigo-800 transition-colors"
              title="View Details"
            >
              <Eye className="w-4 h-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setEditingMember(member);
                setShowAddModal(true);
              }}
              className="text-blue-600 hover:text-blue-800 transition-colors"
              title="Edit member"
            >
              <Edit2 className="w-4 h-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDeactivateMember(member.id);
              }}
              className="text-orange-600 hover:text-orange-800 transition-colors"
              title="Deactivate member"
            >
              <UserX className="w-4 h-4" />
            </button>
          </div>
        </ResponsiveTableCell>
      </ResponsiveTableRow>
    );
  };

  // Render mobile card
  const renderTeamMobileCard = (member: TeamMember) => (
    <div
      key={member.id}
      className="bg-gray-50 p-4 rounded-lg border cursor-pointer hover:bg-gray-100 transition-colors"
      onClick={() => handleMemberClick(member)}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold">
            {member.avatar ? (
              <Image src={member.avatar} alt={member.name} width={48} height={48} className="w-full h-full rounded-full object-cover" />
            ) : (
              member.name.charAt(0)
            )}
          </div>
          <div>
            <p className="font-medium text-gray-900">{member.name}</p>
            <p className="text-sm text-gray-500">{member.email}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${member.status === 'Active' ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className={`text-xs font-medium ${member.status === 'Active' ? 'text-green-600' : 'text-red-600'}`}>
            {member.status === 'Active' ? t('active') : t('inactive')}
          </span>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-2 text-sm">
        <div>
          <span className="text-gray-500">{t('role')}:</span>
          <span className="ml-1 font-medium">
            {member.role === 'Sales' ? t('roles.sales') :
             member.role === 'Purchasing' ? t('roles.purchasing') :
             member.role === 'Delivery' ? t('roles.delivery') :
             member.role === 'Management' ? t('roles.management') :
             member.role === 'Support' ? t('roles.support') : member.role}
          </span>
        </div>
        <div>
          <span className="text-gray-500">{t('phone')}:</span>
          <span className="ml-1">{member.phone}</span>
        </div>
      </div>
      <div className="flex justify-end mt-3 gap-2">
        <button
          onClick={(e) => {
            e.stopPropagation();
            setEditingMember(member);
            setShowAddModal(true);
          }}
          className="text-blue-600 hover:text-blue-800 transition-colors p-1"
          title="Edit member"
        >
          <Edit2 className="w-4 h-4" />
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleDeactivateMember(member.id);
          }}
          className="text-orange-600 hover:text-orange-800 transition-colors p-1"
          title="Deactivate member"
        >
          <UserX className="w-4 h-4" />
        </button>
      </div>
    </div>
  );

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);

  // Load team members
  useEffect(() => {
    if (user) {
      loadTeamMembers();
    }
  }, [user]);

  const loadTeamMembers = async () => {
    try {
      setLoading(true);
      // Use Firebase service instead of local API
      const { TeamService } = await import('@/services/firebase/team');
      const firebaseTeamMembers = await TeamService.getAllTeamMembers();

      // Convert Firebase team members to the format expected by the UI
      const mapFirebaseRoleToUI = (firebaseRole: string): TeamRole => {
        const roleMap: Record<string, TeamRole> = {
          'Sales': 'Sales',
          'Purchasing': 'Purchasing',
          'Delivery': 'Delivery',
          'Administration': 'Management',
          'Customer Service': 'Support'
        };
        return roleMap[firebaseRole] || 'Support';
      };

      const formattedMembers = firebaseTeamMembers.map(member => ({
        id: member.id,
        name: member.name,
        role: mapFirebaseRoleToUI(member.role),
        email: member.email,
        phone: member.phone,
        location: member.department, // Use department as location
        status: member.status as 'Active' | 'Inactive',
        joinDate: member.joinDate.toDate().toISOString().split('T')[0],
        avatar: member.avatar
      }));

      setTeamMembers(formattedMembers);
    } catch (error) {
      console.error('Error loading team members:', error);
      setTeamMembers([]); // Ensure it's always an array
    } finally {
      setLoading(false);
    }
  };

  // Add new member
  const handleAddMember = async (memberData: Omit<TeamMember, 'id'>) => {
    try {
      // Use Firebase service instead of local API
      const { TeamService } = await import('@/services/firebase/team');
      const { Timestamp } = await import('firebase/firestore');

      // Convert UI format to Firebase format
      const mapUIRoleToFirebase = (uiRole: string) => {
        const roleMap: Record<string, 'Sales' | 'Purchasing' | 'Delivery' | 'Customer Service' | 'Administration'> = {
          'Sales': 'Sales',
          'Purchasing': 'Purchasing',
          'Delivery': 'Delivery',
          'Management': 'Administration',
          'Support': 'Customer Service'
        };
        return roleMap[uiRole] || 'Customer Service';
      };

      const firebaseMemberData = {
        name: memberData.name,
        email: memberData.email,
        phone: memberData.phone,
        role: mapUIRoleToFirebase(memberData.role),
        department: memberData.location, // Use location as department
        status: memberData.status as 'Active' | 'Inactive' | 'On Leave',
        joinDate: Timestamp.fromDate(new Date(memberData.joinDate)),
        ...(memberData.avatar && { avatar: memberData.avatar }) // Only include avatar if it exists
      };

      await TeamService.createTeamMember(firebaseMemberData);
      await loadTeamMembers(); // Reload the list
      setShowAddModal(false);
    } catch (error) {
      console.error('Error adding team member:', error);
    }
  };

  // Update member
  const handleUpdateMember = async (memberData: Omit<TeamMember, 'id'>) => {
    if (!editingMember) return;

    try {
      // Use Firebase service instead of local API
      const { TeamService } = await import('@/services/firebase/team');
      const { Timestamp } = await import('firebase/firestore');

      // Convert UI format to Firebase format
      const mapUIRoleToFirebase = (uiRole: string) => {
        const roleMap: Record<string, 'Sales' | 'Purchasing' | 'Delivery' | 'Customer Service' | 'Administration'> = {
          'Sales': 'Sales',
          'Purchasing': 'Purchasing',
          'Delivery': 'Delivery',
          'Management': 'Administration',
          'Support': 'Customer Service'
        };
        return roleMap[uiRole] || 'Customer Service';
      };

      const firebaseUpdates = {
        name: memberData.name,
        email: memberData.email,
        phone: memberData.phone,
        role: mapUIRoleToFirebase(memberData.role),
        department: memberData.location, // Use location as department
        status: memberData.status as 'Active' | 'Inactive' | 'On Leave',
        joinDate: Timestamp.fromDate(new Date(memberData.joinDate)),
        ...(memberData.avatar && { avatar: memberData.avatar }) // Only include avatar if it exists
      };

      await TeamService.updateTeamMember(editingMember.id, firebaseUpdates);
      await loadTeamMembers();
      setEditingMember(null);
    } catch (error) {
      console.error('Error updating team member:', error);
    }
  };

  // Deactivate member (preserves data integrity)
  const handleDeactivateMember = async (id: string) => {
    showConfirmation({
      title: confirmationT('deactivateTeamMember.title'),
      message: confirmationT('deactivateTeamMember.message'),
      type: 'warning',
      confirmText: common('deactivate'),
      showCancel: false, // Use single button mode
      onConfirm: async () => {
        try {
          // Use Firebase service to deactivate instead of delete
          const { TeamService } = await import('@/services/firebase/team');
          await TeamService.deactivateTeamMember(id);
          await loadTeamMembers();
          success(toastT('teamMemberDeactivated'));
        } catch (err) {
          console.error('Error deactivating team member:', err);
          error(toastT('operationFailed'));
        }
      }
    });
  };

  // Legacy method for backward compatibility
  const handleDeleteMember = async (id: string) => {
    return handleDeactivateMember(id);
  };

  // Handle member click
  const handleMemberClick = (member: TeamMember) => {
    setSelectedMember(member);
    setShowDetailsModal(true);
  };

  // Handle edit from details modal
  const handleEditFromDetails = (member: TeamMember) => {
    setEditingMember(member);
    setShowDetailsModal(false);
    setShowAddModal(true);
  };

  // Filter members - ensure teamMembers is always an array
  const filteredMembers = Array.isArray(teamMembers) ? teamMembers.filter(member => {
    const matchesRole = selectedRole === 'All' || member.role === selectedRole;
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = showInactive || member.status === 'Active'; // Hide inactive by default
    return matchesRole && matchesSearch && matchesStatus;
  }) : [];

  // Get role color
  const getRoleColor = (role: TeamRole) => {
    const colors = {
      Sales: 'bg-blue-100 text-blue-800',
      Purchasing: 'bg-green-100 text-green-800',
      Delivery: 'bg-purple-100 text-purple-800',
      Management: 'bg-red-100 text-red-800',
      Support: 'bg-yellow-100 text-yellow-800'
    };
    return colors[role];
  };

  // Get team statistics
  const getTeamStats = () => {
    const membersArray = Array.isArray(teamMembers) ? teamMembers : [];
    const stats = {
      total: membersArray.length,
      active: membersArray.filter(m => m.status === 'Active').length,
      byRole: {} as Record<TeamRole, number>
    };

    membersArray.forEach(member => {
      stats.byRole[member.role] = (stats.byRole[member.role] || 0) + 1;
    });

    return stats;
  };

  const stats = getTeamStats();

  // Show loading spinner while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  // Don't render team page if user is not authenticated
  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <PageTransition transitionKey="team-loading" animationType="fade">
        <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50" data-page-content>
          <Sidebar
            activeItem={activeItem}
            setActiveItem={setActiveItem}
            isOpen={sidebarOpen}
            setIsOpen={setSidebarOpen}
            onNavigate={handleNavigation}
          />
          <div className="lg:ml-64 flex items-center justify-center h-screen">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل أعضاء الفريق...</p>
            </div>
          </div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition transitionKey="team" animationType="fade">
      <div className="h-screen bg-gray-50 text-gray-900" data-page-content>
        <Sidebar
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          isOpen={sidebarOpen}
          setIsOpen={setSidebarOpen}
          onNavigate={handleNavigation}
          onOpenSettings={() => setShowSettings(true)}
        />
        <main className="lg:ml-64 flex flex-col h-full overflow-hidden">
          <Header
            showUserMenu={showUserMenu}
            setShowUserMenu={setShowUserMenu}
            setActiveItem={setActiveItem}
            setShowSettings={setShowSettings}
          />
          <div className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-8 pt-25">
          {/* Creative Add New Team Member Section */}
          <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-3xl p-8 mb-8 text-white relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 left-0 w-40 h-40 bg-white rounded-full -translate-x-20 -translate-y-20"></div>
              <div className="absolute bottom-0 right-0 w-32 h-32 bg-white rounded-full translate-x-16 translate-y-16"></div>
              <div className="absolute top-1/2 left-1/2 w-24 h-24 bg-white rounded-full -translate-x-12 -translate-y-12"></div>
            </div>

            <div className="relative z-10 flex flex-col md:flex-row items-center justify-between">
              <div className="mb-6 md:mb-0">
                <h2 className="text-3xl font-bold mb-2">{t('expandYour')}</h2>
                <p className="text-lg opacity-90 mb-4">{t('addTalented')}</p>
                <div className="flex items-center gap-4 text-sm opacity-80">
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    <span>{t('teamManagement')}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <UserCheck className="w-4 h-4" />
                    <span>{t('roleAssignment')}</span>
                  </div>
                </div>
              </div>

              <button
                onClick={() => setShowAddModal(true)}
                className="bg-white text-indigo-600 px-8 py-4 rounded-2xl font-semibold text-lg hover:bg-gray-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-3"
              >
                <Plus className="w-6 h-6" />
                {t('newMember')}
              </button>
            </div>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{t('totalMembers')}</p>
                  <p className="text-3xl font-bold text-gray-900">
                    {loading ? '...' : stats.total}
                  </p>
                </div>
                <Users className="w-8 h-8 text-indigo-600" />
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{t('activeMembers')}</p>
                  <p className="text-3xl font-bold text-green-600">
                    {loading ? '...' : stats.active}
                  </p>
                </div>
                <UserCheck className="w-8 h-8 text-green-600" />
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{t('inactiveMembers')}</p>
                  <p className="text-3xl font-bold text-red-600">
                    {loading ? '...' : (stats.total - stats.active)}
                  </p>
                </div>
                <UserX className="w-8 h-8 text-red-600" />
              </div>
            </div>
          </div>

        {/* All Team Members Table */}
        <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
          {/* Header */}
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Users className="w-6 h-6 text-gray-600" />
                <h3 className="text-lg font-semibold text-gray-900">{t('title')}</h3>
                <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-sm font-medium">
                  {loading ? '...' : filteredMembers.length}
                </span>
              </div>

              {/* Search and Filters */}
              <div className="flex items-center gap-4">
                <div className="flex flex-wrap gap-2">
                  {(['All', 'Sales', 'Purchasing', 'Delivery', 'Management', 'Support'] as const).map((role) => (
                    <button
                      key={role}
                      onClick={() => setSelectedRole(role)}
                      className={`px-3 py-1 rounded-lg font-medium text-xs transition duration-200 ${
                        selectedRole === role
                          ? 'bg-indigo-600 text-white shadow-lg'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {role === 'All' ? common('all') :
                       role === 'Sales' ? t('roles.sales') :
                       role === 'Purchasing' ? t('roles.purchasing') :
                       role === 'Delivery' ? t('roles.delivery') :
                       role === 'Management' ? t('roles.management') :
                       role === 'Support' ? t('roles.support') : role} {role !== 'All' && `(${stats.byRole[role as TeamRole] || 0})`}
                    </button>
                  ))}
                </div>

                {/* Show Inactive Toggle */}
                <button
                  onClick={() => setShowInactive(!showInactive)}
                  className={`px-3 py-1 rounded-lg font-medium text-xs transition duration-200 flex items-center gap-2 ${
                    showInactive
                      ? 'bg-red-100 text-red-800 border border-red-200'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  title={showInactive ? 'Hide inactive members' : 'Show inactive members'}
                >
                  <UserX className="w-3 h-3" />
                  {showInactive ? 'Hide Inactive' : 'Show Inactive'}
                  {showInactive && ` (${stats.total - stats.active})`}
                </button>

                <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder={t('searchMembers')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 w-64 text-sm"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Responsive Table */}
          <ResponsiveTable
            columns={teamColumns}
            data={filteredMembers}
            renderRow={renderTeamRow}
            renderMobileCard={renderTeamMobileCard}
            loading={loading}
            emptyState={
              <div className="flex flex-col items-center py-12">
                <Users className="w-16 h-16 text-gray-300 mb-4" />
                <p className="text-lg font-medium text-gray-500 mb-2">{t('noTeamMembers')}</p>
                <p className="text-sm text-gray-400">
                  {searchTerm ? t('tryAdjusting') : t('noTeamAvailable')}
                </p>
              </div>
            }
          />
        </div>
        </div>
      </main>

      {/* Team Member Details Modal */}
      <TeamMemberDetailsModal
        member={selectedMember}
        isOpen={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false);
          setSelectedMember(null);
        }}
        onEdit={handleEditFromDetails}
        onDelete={handleDeleteMember}
      />

      {/* Add/Edit Member Modal */}
      <AddEditMemberModal
        isOpen={showAddModal}
        onClose={() => {
          setShowAddModal(false);
          setEditingMember(null);
        }}
        onSave={editingMember ? handleUpdateMember : handleAddMember}
        editingMember={editingMember}
      />

      {/* Settings Modal */}
      <SettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialogComponent />
      </div>
    </PageTransition>
  );
}
'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

// Helper to check if we're in a browser environment
const isBrowser = (): boolean => {
  return typeof window !== 'undefined';
};

export default function LocalePage() {
  const router = useRouter();
  const pathname = usePathname();
  const { user, loading } = useAuth();

  // Extract locale from pathname
  const locale = pathname?.split('/')[1] || 'en';

  useEffect(() => {
    // Skip in non-browser environments
    if (!isBrowser()) return;
    
    if (!loading) {
      if (user) {
        router.push(`/${locale}/dashboard`);
      } else {
        router.push(`/${locale}/login`);
      }
    }
  }, [user, loading, router, locale]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return null;
}

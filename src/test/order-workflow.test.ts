// Test script to verify the complete order workflow fixes
// This tests both the order amount transfer and delivery agent assignment

import { parseMonetaryAmount, debugOrderData, validateOrderData } from '@/utils/orderUtils';

// Test data that mimics the real order from the logs
const testApprovedOrder = {
  id: 'test-approved-001',
  customer: '<PERSON>',
  email: '<EMAIL>',
  phone: '01004823135',
  items: [
    {
      name: 'BOSCH Series 4 Digital Refrigerator, No Frost, 242 Liter, Stainless Steel - GSN36VL30U',
      quantity: 1,
      price: '47999.04'
    }
  ],
  total: '47999.04',
  status: 'Pending',
  orderDate: new Date().toISOString(),
  platform: 'Shopify',
  paymentMethod: 'Credit Card',
  deliveryAddress: 'Palm Hills New Capital Gardens Compound, Unit 53A, New Cairo, Cairo, Egypt 4772001',
  financialStatus: 'paid',
  fulfillmentStatus: 'unfulfilled',
  currency: 'EGP',
  tags: ['test'],
  notes: 'Test order',
  source: 'Shopify',
  approvedDate: new Date().toISOString(),
  originalOrderId: 'Z-1092',
  approvedBy: 'Test User'
};

// Test functions
export function testMonetaryAmountParsing() {
  console.log('🧪 Testing Monetary Amount Parsing...\n');
  
  const testCases = [
    '47999.04',
    'EGP 47999.04',
    '47999.04 EGP',
    '$47999.04',
    '47,999.04',
    'EGP 47,999.04',
    '47999',
    '0',
    '',
    null,
    undefined,
    47999.04
  ];

  testCases.forEach(testCase => {
    try {
      const result = parseMonetaryAmount(testCase as any);
      console.log(`✅ "${testCase}" -> ${result}`);
    } catch (error) {
      console.log(`❌ "${testCase}" -> Error: ${error}`);
    }
  });
  
  console.log('\n');
}

export function testOrderDataValidation() {
  console.log('🧪 Testing Order Data Validation...\n');
  
  // Test valid order data
  const validOrderData = {
    customerName: 'Mohamed Mohsen',
    customerPhone: '01004823135',
    customerAddress: 'Palm Hills New Capital Gardens Compound',
    orderType: 'Online Order',
    totalAmount: 47999.04,
    items: [
      {
        name: 'BOSCH Refrigerator',
        quantity: 1,
        price: 47999.04
      }
    ]
  };

  console.log('Valid Order Data:');
  const validResult = validateOrderData(validOrderData);
  console.log('✅ Validation Result:', validResult);
  console.log('');

  // Test invalid order data
  const invalidOrderData = {
    customerName: '',
    customerPhone: '',
    totalAmount: 0,
    items: [
      {
        name: '',
        quantity: 0,
        price: -100
      }
    ]
  };

  console.log('Invalid Order Data:');
  const invalidResult = validateOrderData(invalidOrderData);
  console.log('❌ Validation Result:', invalidResult);
  console.log('\n');
}

export function testOrderConversion() {
  console.log('🧪 Testing Approved Order to Main Order Conversion...\n');
  
  // Debug the test approved order
  debugOrderData(testApprovedOrder, 'Test Approved Order');
  
  // Simulate the conversion process
  const totalAmount = parseMonetaryAmount(testApprovedOrder.total);
  
  const mainOrderData = {
    orderId: testApprovedOrder.originalOrderId,
    customerName: testApprovedOrder.customer,
    customerPhone: testApprovedOrder.phone,
    customerAddress: testApprovedOrder.deliveryAddress,
    orderType: 'Online Order',
    totalAmount: totalAmount,
    depositAmount: 0,
    status: 'Pending',
    salesRep: 'Online Platform',
    items: testApprovedOrder.items.map(item => ({
      name: item.name,
      quantity: item.quantity,
      price: parseMonetaryAmount(item.price)
    })),
    createdBy: 'Test User',
    creationLocation: { lat: 30.0444, lng: 31.2357 }
  };

  debugOrderData(mainOrderData, 'Converted Main Order Data');
  
  const validation = validateOrderData(mainOrderData);
  console.log('Conversion Validation:', validation);
  
  if (validation.isValid) {
    console.log('✅ Order conversion successful!');
  } else {
    console.log('❌ Order conversion failed:', validation.errors);
  }
  
  console.log('\n');
}

export async function testDeliveryAssignmentAPI() {
  console.log('🧪 Testing Delivery Assignment API...\n');
  
  try {
    // Test getting available agents
    console.log('Testing available agents endpoint...');
    const agentsResponse = await fetch('/api/delivery-assignment?action=available-agents');
    if (agentsResponse.ok) {
      const agentsData = await agentsResponse.json();
      console.log('✅ Available agents:', agentsData.data?.length || 0);
    } else {
      console.log('❌ Failed to get available agents');
    }

    // Test workload endpoint
    console.log('Testing workload endpoint...');
    const workloadResponse = await fetch('/api/delivery-assignment?action=workload');
    if (workloadResponse.ok) {
      const workloadData = await workloadResponse.json();
      console.log('✅ Workload data:', workloadData.data?.length || 0, 'agents');
    } else {
      console.log('❌ Failed to get workload data');
    }

  } catch (error) {
    console.log('❌ API test failed:', error);
  }
  
  console.log('\n');
}

export async function testCompleteWorkflow() {
  console.log('🚀 Testing Complete Order Workflow...\n');
  
  // Step 1: Test monetary parsing
  testMonetaryAmountParsing();
  
  // Step 2: Test validation
  testOrderDataValidation();
  
  // Step 3: Test conversion
  testOrderConversion();
  
  // Step 4: Test delivery assignment API
  await testDeliveryAssignmentAPI();
  
  console.log('🎯 Complete workflow test finished!');
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).orderWorkflowTests = {
    testCompleteWorkflow,
    testMonetaryAmountParsing,
    testOrderDataValidation,
    testOrderConversion,
    testDeliveryAssignmentAPI
  };
  
  console.log('Order workflow tests available in browser console:');
  console.log('- orderWorkflowTests.testCompleteWorkflow()');
  console.log('- orderWorkflowTests.testMonetaryAmountParsing()');
  console.log('- orderWorkflowTests.testOrderDataValidation()');
  console.log('- orderWorkflowTests.testOrderConversion()');
  console.log('- orderWorkflowTests.testDeliveryAssignmentAPI()');
}

// Test script to verify Firebase integration
// This is a manual test file to check if Firebase services are working correctly

import { OnlineOrdersService, OnlineOrder } from '@/services/firebase/onlineOrders';
import { ApprovedOrdersService, ApprovedOrder } from '@/services/firebase/approvedOrders';

// Mock data for testing
const mockOnlineOrder: Omit<OnlineOrder, 'id' | 'createdAt' | 'updatedAt'> = {
  customer: 'Test Customer',
  email: '<EMAIL>',
  phone: '+1234567890',
  items: [
    {
      name: 'Test Product',
      quantity: 2,
      price: '50.00'
    }
  ],
  total: '100.00',
  status: 'Pending',
  orderDate: new Date().toISOString(),
  platform: 'Shopify',
  paymentMethod: 'Credit Card',
  deliveryAddress: '123 Test Street, Test City',
  financialStatus: 'paid',
  fulfillmentStatus: 'unfulfilled',
  currency: 'EGP',
  tags: ['test', 'firebase'],
  notes: 'Test order for Firebase integration',
  source: 'Test',
  originalOrderId: 'TEST-001',
  syncedFromPlatform: false
};

const mockApprovedOrder: Omit<ApprovedOrder, 'id' | 'createdAt' | 'updatedAt'> = {
  customer: 'Test Customer',
  email: '<EMAIL>',
  phone: '+1234567890',
  items: [
    {
      name: 'Test Product',
      quantity: 2,
      price: '50.00'
    }
  ],
  total: '100.00',
  status: 'Pending',
  orderDate: new Date().toISOString(),
  platform: 'Shopify',
  paymentMethod: 'Credit Card',
  deliveryAddress: '123 Test Street, Test City',
  financialStatus: 'paid',
  fulfillmentStatus: 'unfulfilled',
  currency: 'EGP',
  tags: ['test', 'firebase'],
  notes: 'Test approved order for Firebase integration',
  source: 'Test',
  approvedDate: new Date().toISOString(),
  originalOrderId: 'TEST-001',
  approvedBy: 'Test User'
};

// Test functions
export async function testOnlineOrdersService() {
  console.log('Testing Online Orders Service...');
  
  try {
    // Test creating an online order
    console.log('Creating test online order...');
    const orderId = await OnlineOrdersService.createOrUpdateOnlineOrder(mockOnlineOrder);
    console.log('✅ Online order created with ID:', orderId);
    
    // Test fetching the order
    console.log('Fetching created order...');
    const fetchedOrder = await OnlineOrdersService.getOnlineOrder(orderId);
    console.log('✅ Order fetched:', fetchedOrder?.customer);
    
    // Test updating the order
    console.log('Updating order status...');
    await OnlineOrdersService.updateOnlineOrder(orderId, { status: 'Approved' });
    console.log('✅ Order status updated');
    
    // Test fetching all orders
    console.log('Fetching all online orders...');
    const allOrders = await OnlineOrdersService.getAllOnlineOrders();
    console.log('✅ Total online orders:', allOrders.length);
    
    // Clean up - delete test order
    console.log('Cleaning up test order...');
    await OnlineOrdersService.deleteOnlineOrder(orderId);
    console.log('✅ Test order deleted');
    
    return true;
  } catch (error) {
    console.error('❌ Online Orders Service test failed:', error);
    return false;
  }
}

export async function testApprovedOrdersService() {
  console.log('Testing Approved Orders Service...');
  
  try {
    // Test creating an approved order
    console.log('Creating test approved order...');
    const orderId = await ApprovedOrdersService.createApprovedOrder(mockApprovedOrder);
    console.log('✅ Approved order created with ID:', orderId);
    
    // Test fetching the order
    console.log('Fetching created approved order...');
    const fetchedOrder = await ApprovedOrdersService.getApprovedOrder(orderId);
    console.log('✅ Approved order fetched:', fetchedOrder?.customer);
    
    // Test updating the order status
    console.log('Updating approved order status...');
    await ApprovedOrdersService.updateApprovedOrderStatus(orderId, 'Approved', 'Test User');
    console.log('✅ Approved order status updated');
    
    // Test cancelling the order
    console.log('Cancelling approved order...');
    await ApprovedOrdersService.cancelApprovedOrder(orderId, 'Test cancellation', 'Test notes', 'Test User');
    console.log('✅ Approved order cancelled');
    
    // Test fetching all approved orders
    console.log('Fetching all approved orders...');
    const allApprovedOrders = await ApprovedOrdersService.getAllApprovedOrders();
    console.log('✅ Total approved orders:', allApprovedOrders.length);
    
    // Clean up - delete test order
    console.log('Cleaning up test approved order...');
    await ApprovedOrdersService.deleteApprovedOrder(orderId);
    console.log('✅ Test approved order deleted');
    
    return true;
  } catch (error) {
    console.error('❌ Approved Orders Service test failed:', error);
    return false;
  }
}

export async function testAPIEndpoints() {
  console.log('Testing API Endpoints...');
  
  try {
    // Test online orders API
    console.log('Testing online orders API...');
    const onlineOrdersResponse = await fetch('/api/online-orders');
    if (onlineOrdersResponse.ok) {
      const data = await onlineOrdersResponse.json();
      console.log('✅ Online orders API working, returned:', data.total, 'orders');
    } else {
      throw new Error('Online orders API failed');
    }
    
    // Test approved orders API
    console.log('Testing approved orders API...');
    const approvedOrdersResponse = await fetch('/api/approved-orders');
    if (approvedOrdersResponse.ok) {
      const data = await approvedOrdersResponse.json();
      console.log('✅ Approved orders API working, returned:', data.total, 'orders');
    } else {
      throw new Error('Approved orders API failed');
    }
    
    return true;
  } catch (error) {
    console.error('❌ API Endpoints test failed:', error);
    return false;
  }
}

// Main test function
export async function runFirebaseIntegrationTests() {
  console.log('🚀 Starting Firebase Integration Tests...\n');
  
  const results = {
    onlineOrders: false,
    approvedOrders: false,
    apiEndpoints: false
  };
  
  // Run tests
  results.onlineOrders = await testOnlineOrdersService();
  console.log('');
  
  results.approvedOrders = await testApprovedOrdersService();
  console.log('');
  
  results.apiEndpoints = await testAPIEndpoints();
  console.log('');
  
  // Summary
  console.log('📊 Test Results Summary:');
  console.log('Online Orders Service:', results.onlineOrders ? '✅ PASSED' : '❌ FAILED');
  console.log('Approved Orders Service:', results.approvedOrders ? '✅ PASSED' : '❌ FAILED');
  console.log('API Endpoints:', results.apiEndpoints ? '✅ PASSED' : '❌ FAILED');
  
  const allPassed = Object.values(results).every(result => result);
  console.log('\n🎯 Overall Result:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
  
  return allPassed;
}

// Export for use in browser console or other test runners
if (typeof window !== 'undefined') {
  (window as any).firebaseTests = {
    runFirebaseIntegrationTests,
    testOnlineOrdersService,
    testApprovedOrdersService,
    testAPIEndpoints
  };
}

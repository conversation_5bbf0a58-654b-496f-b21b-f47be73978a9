// Utility functions for order processing

/**
 * Parse monetary amount from various string formats
 * Handles formats like: "100.00", "EGP 100.00", "100.00 EGP", "$100.00", etc.
 */
export function parseMonetaryAmount(amount: string | number): number {
  if (typeof amount === 'number') {
    return amount;
  }

  if (!amount || typeof amount !== 'string') {
    console.warn('Invalid amount provided:', amount);
    return 0;
  }

  try {
    // Remove all non-digit and non-decimal characters
    const cleanAmount = amount.toString().replace(/[^\d.]/g, '');
    
    // Handle empty string after cleaning
    if (!cleanAmount) {
      console.warn('Amount became empty after cleaning:', amount);
      return 0;
    }

    const parsed = parseFloat(cleanAmount);
    
    if (isNaN(parsed)) {
      console.warn('Failed to parse amount:', amount, '-> cleaned:', cleanAmount);
      return 0;
    }

    console.log(`Parsed amount: "${amount}" -> ${parsed}`);
    return parsed;
  } catch (error) {
    console.error('Error parsing monetary amount:', error, 'Input:', amount);
    return 0;
  }
}

/**
 * Format monetary amount for display
 */
export function formatMonetaryAmount(amount: number, currency: string = 'EGP'): string {
  if (isNaN(amount) || amount < 0) {
    return `0.00 ${currency}`;
  }

  return `${amount.toFixed(2)} ${currency}`;
}

/**
 * Validate order data before processing
 */
export function validateOrderData(orderData: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Required fields
  if (!orderData.customerName) errors.push('Customer name is required');
  if (!orderData.customerPhone) errors.push('Customer phone is required');
  if (!orderData.customerAddress) errors.push('Customer address is required');
  if (!orderData.orderType) errors.push('Order type is required');

  // Validate total amount
  if (orderData.totalAmount === undefined || orderData.totalAmount === null) {
    errors.push('Total amount is required');
  } else {
    const amount = parseMonetaryAmount(orderData.totalAmount);
    if (amount <= 0) {
      errors.push('Total amount must be greater than 0');
    }
  }

  // Validate items if provided
  if (orderData.items && Array.isArray(orderData.items)) {
    orderData.items.forEach((item: any, index: number) => {
      if (!item.name) errors.push(`Item ${index + 1}: name is required`);
      if (!item.quantity || item.quantity <= 0) errors.push(`Item ${index + 1}: quantity must be greater than 0`);
      if (item.price !== undefined) {
        const price = parseMonetaryAmount(item.price);
        if (price < 0) errors.push(`Item ${index + 1}: price cannot be negative`);
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Generate unique order ID
 */
export function generateOrderId(existingOrderCount: number = 0): string {
  const orderNumber = existingOrderCount + 1;
  return `ORD-${String(orderNumber).padStart(3, '0')}`;
}

/**
 * Calculate order total from items
 */
export function calculateOrderTotal(items: Array<{ quantity: number; price: string | number }>): number {
  if (!Array.isArray(items) || items.length === 0) {
    return 0;
  }

  return items.reduce((total, item) => {
    const price = parseMonetaryAmount(item.price);
    const quantity = typeof item.quantity === 'number' ? item.quantity : 0;
    return total + (price * quantity);
  }, 0);
}

/**
 * Sanitize order data for Firebase storage
 */
export function sanitizeOrderData(orderData: any): any {
  const sanitized = { ...orderData };

  // Ensure totalAmount is a number
  if (sanitized.totalAmount !== undefined) {
    sanitized.totalAmount = parseMonetaryAmount(sanitized.totalAmount);
  }

  // Ensure depositAmount is a number
  if (sanitized.depositAmount !== undefined) {
    sanitized.depositAmount = parseMonetaryAmount(sanitized.depositAmount);
  }

  // Sanitize items
  if (sanitized.items && Array.isArray(sanitized.items)) {
    sanitized.items = sanitized.items.map((item: any) => ({
      ...item,
      quantity: typeof item.quantity === 'number' ? item.quantity : parseInt(item.quantity) || 0,
      price: parseMonetaryAmount(item.price)
    }));
  }

  // Remove undefined values
  Object.keys(sanitized).forEach(key => {
    if (sanitized[key] === undefined) {
      delete sanitized[key];
    }
  });

  return sanitized;
}

/**
 * Debug order data - logs detailed information about order structure
 */
export function debugOrderData(orderData: any, context: string = 'Order Debug'): void {
  console.group(`🔍 ${context}`);
  
  console.log('Raw order data:', orderData);
  
  if (orderData.total || orderData.totalAmount) {
    const totalField = orderData.total || orderData.totalAmount;
    console.log(`Total field: "${totalField}" (type: ${typeof totalField})`);
    console.log(`Parsed total: ${parseMonetaryAmount(totalField)}`);
  }

  if (orderData.items && Array.isArray(orderData.items)) {
    console.log('Items:');
    orderData.items.forEach((item: any, index: number) => {
      console.log(`  Item ${index + 1}:`, {
        name: item.name,
        quantity: item.quantity,
        price: item.price,
        parsedPrice: parseMonetaryAmount(item.price)
      });
    });
    
    const calculatedTotal = calculateOrderTotal(orderData.items);
    console.log(`Calculated total from items: ${calculatedTotal}`);
  }

  const validation = validateOrderData(orderData);
  console.log('Validation result:', validation);
  
  console.groupEnd();
}

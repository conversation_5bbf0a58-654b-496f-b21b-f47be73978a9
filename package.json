{"name": "zawaya-delivery", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@next-auth/supabase-adapter": "^0.2.1", "@shopify/shopify-api": "^11.13.0", "@supabase/supabase-js": "^2.49.8", "firebase": "^11.8.1", "lodash": "^4.17.21", "lucide-react": "^0.517.0", "next": "^15.3.4", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "recharts": "^2.15.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^24.0.3", "@types/nodemailer": "^6.4.14", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "^15.3.3", "firebase-tools": "^14.4.0", "tailwindcss": "^4", "typescript": "^5", "vercel": "^43.3.0"}}